# Subscription Upgrade with BayarCash E-Mandate

## Overview

The subscription upgrade system has been modified to exclusively use BayarCash E-Mandate (FPX Direct Debit) as the payment method. This provides a streamlined, secure, and automated payment experience for users upgrading their subscription plans.

## Key Features

### 1. Exclusive E-Mandate Payment
- **Single Payment Method**: Only BayarCash E-Mandate is available for subscription upgrades
- **No Payment Gateway Selection**: Removed all other payment options (Bizappay, regular BayarCash, etc.)
- **Consistent Experience**: Same e-mandate flow as new subscriptions

### 2. Pro-Rated Upgrade Calculation
- **Smart Pricing**: Calculates upgrade amount based on plan price difference
- **Pro-Rating Logic**: Considers remaining time in current subscription period
- **Minimum Amount**: Ensures upgrade amount is never negative

### 3. Upgrade Flow Integration
- **Seamless Process**: Integrates with existing `SubscriptionEMandateService`
- **Status Handling**: Properly handles the BayarCash status=0 routing issue
- **Error Recovery**: Rollback mechanism if e-mandate enrollment fails

## Technical Implementation

### Database Changes

New fields added to `subscriptions` table:
```sql
- upgrade_amount (decimal): The calculated upgrade amount
- is_upgrade (boolean): Flag indicating if this is an upgrade subscription
- previous_subscription_id (uuid): Reference to the subscription being upgraded from
```

### Service Layer Updates

#### SubscriptionEMandateService
- **Enhanced Amount Calculation**: Uses upgrade_amount for upgrade subscriptions
- **Metadata Tracking**: Includes upgrade information in e-mandate metadata
- **Existing E-Mandate Detection**: Checks for compatible existing enrollments

#### SubscriptionManagementController
- **Upgrade Amount Calculation**: `calculateUpgradeAmount()` method with pro-rating
- **E-Mandate Integration**: Direct integration with `SubscriptionEMandateService`
- **Error Handling**: Comprehensive error handling and rollback logic

### User Interface Updates

#### Upgrade Form (`resources/views/user/subscriptions/upgrade.blade.php`)
- **Payment Method Information**: Clear explanation of e-mandate process
- **Security Assurance**: Information about bank-level security
- **Process Explanation**: Step-by-step guidance for users

## Upgrade Process Flow

### 1. User Initiates Upgrade
```
User selects upgrade plan → Upgrade form displays → User confirms upgrade
```

### 2. Backend Processing
```
Calculate upgrade amount → Create new subscription (pending) → 
Cancel current subscription → Create e-mandate enrollment
```

### 3. E-Mandate Enrollment
```
Redirect to BayarCash → User completes FPX enrollment → 
Handle return URL → Activate subscription
```

### 4. Status Handling
```
Status=0 (Pending) → Redirect to success (workaround for BayarCash routing issue) →
Show success message → Subscription activated
```

## Error Handling

### E-Mandate Enrollment Failure
- Rollback new subscription creation
- Restore current subscription status
- Display user-friendly error message

### BayarCash Routing Issue
- Automatic detection of misrouted status=0
- Redirect from failed URL to success URL
- Proper status interpretation and logging

## Configuration

### Required Environment Variables
```env
BC_PORTAL_KEY=your_bayarcash_portal_key
BC_API_TOKEN=your_bayarcash_api_token
BC_API_SECRET_KEY=your_bayarcash_secret_key
BC_SANDBOX=true/false
```

### Route Configuration
```php
Route::get('/subscription-management/upgrade/{planId}', [SubscriptionManagementController::class, 'upgradeForm']);
Route::post('/subscription-management/upgrade/{planId}', [SubscriptionManagementController::class, 'processUpgrade']);
```

## Testing Considerations

### Sandbox Environment
- BayarCash sandbox has a known issue where status=0 is routed to failed_url
- The system includes a workaround that automatically redirects to success
- Test both successful and failed enrollment scenarios

### Production Environment
- Verify if the status=0 routing issue exists in production
- Monitor callback logs to ensure proper callback handling
- Test pro-rating calculations with various subscription periods

## Benefits

1. **Simplified User Experience**: Single payment method reduces confusion
2. **Automated Payments**: E-mandate enables automatic recurring payments
3. **Bank-Level Security**: Direct bank-to-bank transfers
4. **Consistent Flow**: Same process as new subscription enrollments
5. **Error Recovery**: Robust error handling and rollback mechanisms

## Future Enhancements

1. **Callback Monitoring**: Enhanced logging for callback debugging
2. **Production Testing**: Verify BayarCash behavior in production environment
3. **User Notifications**: Email notifications for upgrade status updates
4. **Admin Dashboard**: Upgrade tracking and management interface
