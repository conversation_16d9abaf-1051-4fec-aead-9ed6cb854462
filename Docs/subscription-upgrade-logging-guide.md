# Subscription Upgrade E-Mandate Logging System

## Overview

This comprehensive logging system provides complete visibility into the subscription upgrade process with BayarCash E-Mandate integration. It captures all critical events, API interactions, and error conditions for production monitoring and debugging.

## Log File Location

**Primary Log File**: `storage/logs/subscription-upgrade-emandate-YYYY-MM-DD.log`

The system uses daily log rotation with 30-day retention to prevent disk space issues.

## Log Levels and Usage

### INFO Level
- Normal flow events (upgrade initiation, completion)
- Successful API responses
- Status updates and transitions

### WARNING Level  
- Recoverable issues (status=0 misrouting)
- Callback/return URL handling anomalies
- Non-critical configuration issues

### ERROR Level
- Failed API requests
- E-mandate enrollment failures
- Rollback operations
- Critical system errors

### DEBUG Level
- Detailed API request/response data
- Internal calculations and validations
- SDK interactions and data transformations

## Key Events Tracked

### 1. Upgrade Process Flow
```
upgrade_initiated → upgrade_amount_calculated → new_subscription_created → 
emandate_enrollment_attempt → bayarcash_api_request → bayarcash_api_response → 
upgrade_completed
```

### 2. BayarCash API Interactions
- **Request Preparation**: `bayarcash_api_request_prepared`
- **API Calls**: `bayarcash_api_request` / `bayarcash_api_response`
- **Callback Handling**: `bayarcash_callback_received`
- **Return URL Processing**: `bayarcash_success_return` / `bayarcash_failed_return`

### 3. Status Code Interpretation
- **Status Analysis**: `status_interpretation`
- **Misrouting Detection**: `misrouted_pending_status_detected`
- **Corrective Actions**: Automatic redirects and status corrections

### 4. Error Handling
- **Enrollment Failures**: `emandate_enrollment_failed`
- **Rollback Operations**: `upgrade_rollback`
- **Exception Tracking**: Complete stack traces with context

## Log Entry Structure

Each log entry includes:

```json
{
  "timestamp": "2025-07-07 10:30:45",
  "level": "INFO",
  "event": "upgrade_initiated",
  "message": "=== SUBSCRIPTION UPGRADE INITIATED ===",
  "context": {
    "user_id": 123,
    "company_id": 456,
    "current_subscription": {...},
    "new_plan": {...},
    "upgrade_amount": 25.00
  },
  "extra": {
    "request_id": "req_abc123",
    "user_email": "<EMAIL>",
    "timestamp_unix": **********,
    "memory_usage": "12MB"
  }
}
```

## Monitoring and Alerting

### Critical Events to Monitor

1. **High Error Rates**
   ```bash
   grep -c "ERROR" storage/logs/subscription-upgrade-emandate-$(date +%Y-%m-%d).log
   ```

2. **Status=0 Misrouting Frequency**
   ```bash
   grep -c "misrouted_pending_status_detected" storage/logs/subscription-upgrade-emandate-*.log
   ```

3. **API Response Failures**
   ```bash
   grep "emandate_enrollment_failed" storage/logs/subscription-upgrade-emandate-*.log
   ```

4. **Rollback Operations**
   ```bash
   grep "upgrade_rollback" storage/logs/subscription-upgrade-emandate-*.log
   ```

### Production Monitoring Commands

**Real-time Monitoring**:
```bash
tail -f storage/logs/subscription-upgrade-emandate-$(date +%Y-%m-%d).log | grep -E "(ERROR|WARNING|upgrade_initiated|upgrade_completed)"
```

**Daily Summary**:
```bash
# Count events by type
grep -o '"event":"[^"]*"' storage/logs/subscription-upgrade-emandate-$(date +%Y-%m-%d).log | sort | uniq -c

# Error summary
grep "ERROR" storage/logs/subscription-upgrade-emandate-$(date +%Y-%m-%d).log | grep -o '"message":"[^"]*"' | sort | uniq -c
```

## Debugging Workflows

### 1. Subscription Upgrade Issues
```bash
# Find all events for a specific subscription
grep "subscription_id.*YOUR_SUBSCRIPTION_ID" storage/logs/subscription-upgrade-emandate-*.log

# Track upgrade flow for a user
grep "user_id.*YOUR_USER_ID" storage/logs/subscription-upgrade-emandate-*.log | grep -E "(upgrade_initiated|upgrade_completed|upgrade_rollback)"
```

### 2. BayarCash Integration Issues
```bash
# Find callback/return URL issues
grep -E "(bayarcash_callback|bayarcash_.*_return)" storage/logs/subscription-upgrade-emandate-*.log

# Track specific order number
grep "order_number.*YOUR_ORDER_NUMBER" storage/logs/subscription-upgrade-emandate-*.log
```

### 3. Status Code Problems
```bash
# Find status interpretation issues
grep "status_interpretation" storage/logs/subscription-upgrade-emandate-*.log

# Find misrouted status=0 cases
grep "misrouted_pending_status" storage/logs/subscription-upgrade-emandate-*.log
```

## Log Analysis Tools

### 1. JSON Log Parser Script
```bash
#!/bin/bash
# parse-upgrade-logs.sh
LOG_FILE="storage/logs/subscription-upgrade-emandate-$(date +%Y-%m-%d).log"

echo "=== Upgrade Events Summary ==="
grep -o '"event":"[^"]*"' "$LOG_FILE" | cut -d'"' -f4 | sort | uniq -c | sort -nr

echo -e "\n=== Error Messages ==="
grep "ERROR" "$LOG_FILE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4 | sort | uniq -c

echo -e "\n=== Status Code Distribution ==="
grep "status_interpretation" "$LOG_FILE" | grep -o '"raw_status":"[^"]*"' | cut -d'"' -f4 | sort | uniq -c
```

### 2. Performance Metrics
```bash
# Average upgrade completion time
grep -E "(upgrade_initiated|upgrade_completed)" storage/logs/subscription-upgrade-emandate-*.log | 
awk '/upgrade_initiated/{start=$1" "$2} /upgrade_completed/{end=$1" "$2; print end" - "start}'
```

## Configuration

### Environment Variables
```env
LOG_LEVEL=debug
LOG_CHANNEL=subscription_upgrade_emandate
```

### Laravel Configuration
```php
// config/logging.php
'subscription_upgrade_emandate' => [
    'driver' => 'daily',
    'path' => storage_path('logs/subscription-upgrade-emandate.log'),
    'level' => env('LOG_LEVEL', 'debug'),
    'days' => 30,
    'permission' => 0664,
    'tap' => [App\Logging\SubscriptionUpgradeEMandateFormatter::class],
],
```

## Testing the Logging System

### 1. Run the Test Suite
```bash
php artisan test tests/Feature/SubscriptionUpgradeLoggingTest.php
```

### 2. Manual Testing
```bash
# Test log channel
php artisan tinker
>>> use App\Services\SubscriptionUpgradeLogger;
>>> SubscriptionUpgradeLogger::logError('test', new Exception('Test message'));
>>> exit

# Check log file
tail storage/logs/subscription-upgrade-emandate-$(date +%Y-%m-%d).log
```

## Troubleshooting

### Common Issues

1. **Log File Not Created**
   - Check storage directory permissions: `chmod 755 storage/logs`
   - Verify log channel configuration in `config/logging.php`

2. **Missing Log Entries**
   - Confirm LOG_LEVEL environment variable
   - Check if the correct channel is being used in code

3. **Large Log Files**
   - Verify daily rotation is working
   - Adjust retention period if needed
   - Consider log level optimization for production

### Performance Considerations

- **Production**: Use INFO level or higher to reduce log volume
- **Development**: Use DEBUG level for detailed troubleshooting
- **Log Rotation**: Ensure daily rotation prevents disk space issues
- **Monitoring**: Set up alerts for ERROR level events

## Integration with Monitoring Tools

### ELK Stack Integration
```json
{
  "filebeat": {
    "inputs": [
      {
        "type": "log",
        "paths": ["storage/logs/subscription-upgrade-emandate-*.log"],
        "fields": {
          "service": "subscription-upgrade",
          "environment": "production"
        }
      }
    ]
  }
}
```

### Grafana Dashboard Queries
```sql
-- Upgrade success rate
SELECT 
  COUNT(CASE WHEN event = 'upgrade_completed' AND outcome = 'success' THEN 1 END) * 100.0 / 
  COUNT(CASE WHEN event = 'upgrade_initiated' THEN 1 END) as success_rate
FROM logs 
WHERE timestamp >= NOW() - INTERVAL 1 DAY;

-- Error rate by hour
SELECT 
  DATE_TRUNC('hour', timestamp) as hour,
  COUNT(CASE WHEN level = 'ERROR' THEN 1 END) as error_count
FROM logs 
WHERE timestamp >= NOW() - INTERVAL 24 HOUR
GROUP BY hour;
```

This comprehensive logging system provides complete visibility into the subscription upgrade process, enabling effective monitoring, debugging, and performance optimization in production environments.
