<?php

namespace App\Models\Subscription;

use App\Traits\UUID;
use App\Models\Company;
use App\Models\Feature;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\Models\Subscription\SubscriptionPlan;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Subscription extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $fillable = [
        'company_id',
        'subscription_plan_id',
        'starts_at',
        'ends_at',
        'bill_id',
        'bill_type',
        'emandate_enrollment_id',
        'status',
        'trial_ends_at',
        'cancels_at',
        'canceled_at',
        'upgrade_amount',
        'is_upgrade',
        'previous_subscription_id'
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancels_at' => 'datetime',
        'canceled_at' => 'datetime',
        'upgrade_amount' => 'decimal:2',
        'is_upgrade' => 'boolean'
    ];

    /**
     * Get the company that owns the subscription.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the plan that the subscription belongs to.
     */
    public function subscriptionPlan()
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the e-mandate enrollment associated with this subscription.
     */
    public function emandateEnrollment()
    {
        return $this->belongsTo(\App\Models\Payment\BayarCashEMandate::class, 'emandate_enrollment_id');
    }

    /**
     * Get the previous subscription that this subscription upgraded from.
     */
    public function previousSubscription()
    {
        return $this->belongsTo(Subscription::class, 'previous_subscription_id');
    }

    /**
     * Get subscriptions that upgraded from this subscription.
     */
    public function upgradedSubscriptions()
    {
        return $this->hasMany(Subscription::class, 'previous_subscription_id');
    }

    /**
     * Get all features available to this subscription through its plan.
     */
    public function features()
    {
        return $this->subscriptionPlan->features();
    }

    /**
     * Determine if the subscription is active.
     */
    public function isActive()
    {
        return $this->status === 'active' &&
               ($this->ends_at === null || $this->ends_at->isFuture());
    }

    /**
     * Determine if the subscription is within its trial period.
     */
    public function onTrial()
    {
        return $this->trial_ends_at !== null &&
               $this->trial_ends_at->isFuture();
    }

    /**
     * Determine if the subscription is canceled.
     */
    public function canceled()
    {
        return $this->canceled_at !== null;
    }

    /**
     * Determine if the subscription has ended.
     */
    public function ended()
    {
        return $this->canceled() &&
               ($this->ends_at !== null && $this->ends_at->isPast());
    }

    /**
     * Cancel the subscription.
     */
    public function cancel($immediately = false)
    {
        $this->canceled_at = Carbon::now();

        if ($immediately) {
            $this->ends_at = Carbon::now();
            $this->status = 'canceled';
        } else {
            $this->cancels_at = $this->ends_at;
        }

        $this->save();

        return $this;
    }

    /**
     * Resume the subscription.
     */
    public function resume()
    {
        if (!$this->canceled()) {
            return $this;
        }

        $this->canceled_at = null;
        $this->cancels_at = null;
        $this->status = 'active';

        $this->save();

        return $this;
    }

    /**
     * Check if the subscription has a specific feature.
     */
    public function hasFeature($featureCode)
    {
        if (!$this->isActive()) {
            return false;
        }

        return $this->features()
                    ->where('code', $featureCode)
                    ->where('is_active', true)
                    ->exists();
    }

    /**
     * Scope a query to only include active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                     ->where(function ($query) {
                         $query->whereNull('ends_at')
                               ->orWhere('ends_at', '>', Carbon::now());
                     });
    }
}
