<?php

namespace App\Services;

use GuzzleHttp\Client as HttpClient;
use Webimpian\BayarcashSdk\Bayarcash;

class CustomBayarCashSDK extends Bayarcash
{
    /**
     * @var string
     */
    protected $customBaseUrl;

    /**
     * Constructor
     *
     * @param string|null $token
     * @param string|null $customBaseUrl
     * @throws \InvalidArgumentException
     */
    public function __construct(?string $token, ?string $customBaseUrl = null)
    {
        // Validate token before proceeding
        if (empty($token)) {
            \Illuminate\Support\Facades\Log::error('CustomBayarCashSDK: API token is null or empty', [
                'token_provided' => $token === null ? 'null' : 'empty_string',
                'config_value' => config('services.bayarcash.api_token'),
                'env_bc_api_token' => env('BC_API_TOKEN'),
                'environment' => app()->environment(),
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
            ]);

            throw new \InvalidArgumentException(
                'BayarCash API token is required but not provided. Please check your BC_API_TOKEN environment variable.'
            );
        }

        $this->customBaseUrl = $customBaseUrl;

        // Call parent constructor to properly initialize the SDK
        parent::__construct($token);

        // Override with our custom initialization if needed
        if ($customBaseUrl) {
            $this->initializeGuzzle();
        }
    }

    /**
     * Initialize or reinitialize the Guzzle HTTP client.
     *
     * @return void
     */
    private function initializeGuzzle()
    {
        // Log the base URI and token being used
        \Illuminate\Support\Facades\Log::debug('CustomBayarCashSDK initializing Guzzle', [
            'base_uri' => $this->customBaseUrl ?: $this->getBaseUri(),
            'token_length' => strlen($this->token),
            // 'token_prefix' => substr($this->token, 0, 20) . '...'
            'token_prefix' => $this->token
        ]);

        $this->guzzle = new HttpClient([
            'base_uri' => $this->customBaseUrl ?: $this->getBaseUri(),
            'http_errors' => false,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    /**
     * Override the getBaseUri method to use our custom base URL
     *
     * @return string
     */
    protected function getBaseUri()
    {
        if ($this->customBaseUrl) {
            return $this->customBaseUrl;
        }

        return parent::getBaseUri();
    }

    /**
     * Set a custom base URL
     *
     * @param string $url
     * @return $this
     */
    public function setBaseUrl(string $url)
    {
        $this->customBaseUrl = $url;
        $this->initializeGuzzle();
        return $this;
    }
}
