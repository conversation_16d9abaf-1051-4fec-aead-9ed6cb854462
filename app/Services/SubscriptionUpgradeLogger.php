<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Company;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Payment\BayarCashEMandate;

class SubscriptionUpgradeLogger
{
    private const LOG_CHANNEL = 'subscription_upgrade_emandate';

    /**
     * Log upgrade initiation
     */
    public static function logUpgradeInitiation(
        User $user,
        Company $company,
        Subscription $currentSubscription,
        SubscriptionPlan $newPlan,
        float $upgradeAmount
    ): void {
        Log::channel(self::LOG_CHANNEL)->info('=== SUBSCRIPTION UPGRADE INITIATED ===', [
            'event' => 'upgrade_initiated',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'company_id' => $company->id,
            'current_subscription' => [
                'id' => $currentSubscription->id,
                'plan_id' => $currentSubscription->subscription_plan_id,
                'plan_name' => $currentSubscription->subscriptionPlan->name,
                'current_price' => $currentSubscription->subscriptionPlan->price,
                'starts_at' => $currentSubscription->starts_at->toISOString(),
                'ends_at' => $currentSubscription->ends_at?->toISOString(),
                'status' => $currentSubscription->status
            ],
            'new_plan' => [
                'id' => $newPlan->id,
                'name' => $newPlan->name,
                'price' => $newPlan->price,
                'billing_frequency' => $newPlan->billing_frequency,
                'tier' => $newPlan->tier
            ],
            'upgrade_calculation' => [
                'price_difference' => $newPlan->price - $currentSubscription->subscriptionPlan->price,
                'calculated_upgrade_amount' => $upgradeAmount,
                'calculation_method' => 'pro_rated'
            ]
        ]);
    }

    /**
     * Log upgrade amount calculation details
     */
    public static function logUpgradeAmountCalculation(
        Subscription $currentSubscription,
        SubscriptionPlan $newPlan,
        float $upgradeAmount,
        array $calculationDetails = []
    ): void {
        Log::channel(self::LOG_CHANNEL)->debug('Upgrade amount calculation performed', [
            'event' => 'upgrade_amount_calculated',
            'current_subscription_id' => $currentSubscription->id,
            'new_plan_id' => $newPlan->id,
            'calculation' => array_merge([
                'current_plan_price' => $currentSubscription->subscriptionPlan->price,
                'new_plan_price' => $newPlan->price,
                'base_difference' => $newPlan->price - $currentSubscription->subscriptionPlan->price,
                'final_upgrade_amount' => $upgradeAmount
            ], $calculationDetails)
        ]);
    }

    /**
     * Log new subscription creation
     */
    public static function logNewSubscriptionCreated(Subscription $newSubscription): void
    {
        Log::channel(self::LOG_CHANNEL)->info('New upgrade subscription created', [
            'event' => 'new_subscription_created',
            'new_subscription' => [
                'id' => $newSubscription->id,
                'plan_id' => $newSubscription->subscription_plan_id,
                'plan_name' => $newSubscription->subscriptionPlan->name,
                'company_id' => $newSubscription->company_id,
                'status' => $newSubscription->status,
                'is_upgrade' => $newSubscription->is_upgrade,
                'upgrade_amount' => $newSubscription->upgrade_amount,
                'previous_subscription_id' => $newSubscription->previous_subscription_id,
                'starts_at' => $newSubscription->starts_at->toISOString(),
                'ends_at' => $newSubscription->ends_at?->toISOString()
            ]
        ]);
    }

    /**
     * Log e-mandate enrollment attempt
     */
    public static function logEMandateEnrollmentAttempt(
        Subscription $subscription,
        array $enrollmentData
    ): void {
        Log::channel(self::LOG_CHANNEL)->info('E-mandate enrollment attempt started', [
            'event' => 'emandate_enrollment_attempt',
            'subscription_id' => $subscription->id,
            'enrollment_data' => [
                'amount' => $enrollmentData['amount'] ?? null,
                'frequency_mode' => $enrollmentData['frequency_mode'] ?? null,
                'payer_email' => $enrollmentData['payer_email'] ?? null,
                'application_reason' => $enrollmentData['application_reason'] ?? null,
                'effective_date' => $enrollmentData['effective_date'] ?? null,
                'metadata' => $enrollmentData['metadata'] ?? null
            ]
        ]);
    }

    /**
     * Log e-mandate enrollment result
     */
    public static function logEMandateEnrollmentResult(
        Subscription $subscription,
        array $result,
        ?BayarCashEMandate $enrollment = null
    ): void {
        $logLevel = $result['success'] ? 'info' : 'error';
        
        Log::channel(self::LOG_CHANNEL)->$logLevel('E-mandate enrollment result', [
            'event' => 'emandate_enrollment_result',
            'subscription_id' => $subscription->id,
            'result' => [
                'success' => $result['success'],
                'message' => $result['message'],
                'is_existing' => $result['is_existing'] ?? false,
                'redirect_url' => $result['redirect_url'] ?? null
            ],
            'enrollment' => $enrollment ? [
                'id' => $enrollment->id,
                'order_number' => $enrollment->order_number,
                'amount' => $enrollment->amount,
                'status' => $enrollment->status,
                'enrollment_url' => $enrollment->enrollment_url
            ] : null
        ]);
    }

    /**
     * Log BayarCash API request
     */
    public static function logBayarCashApiRequest(
        string $endpoint,
        array $requestData,
        string $orderNumber = null
    ): void {
        Log::channel(self::LOG_CHANNEL)->debug('BayarCash API request sent', [
            'event' => 'bayarcash_api_request',
            'endpoint' => $endpoint,
            'order_number' => $orderNumber,
            'request_data_keys' => array_keys($requestData),
            'request_data' => array_merge($requestData, [
                // Mask sensitive data
                'portal_key' => isset($requestData['portal_key']) ? substr($requestData['portal_key'], 0, 8) . '...' : null,
                'payer_id' => isset($requestData['payer_id']) ? substr($requestData['payer_id'], 0, 4) . '...' : null
            ])
        ]);
    }

    /**
     * Log BayarCash API response
     */
    public static function logBayarCashApiResponse(
        string $endpoint,
        $response,
        string $orderNumber = null
    ): void {
        Log::channel(self::LOG_CHANNEL)->debug('BayarCash API response received', [
            'event' => 'bayarcash_api_response',
            'endpoint' => $endpoint,
            'order_number' => $orderNumber,
            'response_type' => gettype($response),
            'response_data' => is_object($response) ? get_class($response) : $response
        ]);
    }

    /**
     * Log callback/return URL handling
     */
    public static function logCallbackHandling(
        string $type, // 'callback', 'success_return', 'failed_return'
        array $requestData,
        ?BayarCashEMandate $enrollment = null
    ): void {
        $logLevel = $type === 'failed_return' ? 'warning' : 'info';
        
        Log::channel(self::LOG_CHANNEL)->$logLevel("BayarCash {$type} handling", [
            'event' => "bayarcash_{$type}_handling",
            'request_data' => [
                'order_number' => $requestData['order_number'] ?? null,
                'status' => $requestData['status'] ?? null,
                'transaction_id' => $requestData['transaction_id'] ?? null,
                'amount' => $requestData['amount'] ?? null,
                'currency' => $requestData['currency'] ?? null,
                'payer_bank_name' => $requestData['payer_bank_name'] ?? null,
                'exchange_reference_number' => $requestData['exchange_reference_number'] ?? null,
                'checksum_provided' => isset($requestData['checksum'])
            ],
            'enrollment' => $enrollment ? [
                'id' => $enrollment->id,
                'status' => $enrollment->status,
                'amount' => $enrollment->amount
            ] : null
        ]);
    }

    /**
     * Log status code interpretation
     */
    public static function logStatusInterpretation(
        string $status,
        array $interpretation,
        string $orderNumber = null
    ): void {
        $logLevel = $interpretation['is_pending'] ? 'warning' : 
                   ($interpretation['is_success'] ? 'info' : 'error');
        
        Log::channel(self::LOG_CHANNEL)->$logLevel('BayarCash status code interpretation', [
            'event' => 'status_interpretation',
            'order_number' => $orderNumber,
            'raw_status' => $status,
            'interpretation' => $interpretation
        ]);
    }

    /**
     * Log upgrade completion
     */
    public static function logUpgradeCompletion(
        Subscription $newSubscription,
        string $outcome, // 'success', 'pending', 'failed'
        string $message = null
    ): void {
        $logLevel = $outcome === 'success' ? 'info' : ($outcome === 'pending' ? 'warning' : 'error');
        
        Log::channel(self::LOG_CHANNEL)->$logLevel('=== SUBSCRIPTION UPGRADE COMPLETED ===', [
            'event' => 'upgrade_completed',
            'outcome' => $outcome,
            'message' => $message,
            'subscription' => [
                'id' => $newSubscription->id,
                'status' => $newSubscription->status,
                'plan_name' => $newSubscription->subscriptionPlan->name,
                'emandate_enrollment_id' => $newSubscription->emandate_enrollment_id
            ]
        ]);
    }

    /**
     * Log upgrade rollback
     */
    public static function logUpgradeRollback(
        Subscription $newSubscription,
        Subscription $currentSubscription,
        string $reason
    ): void {
        Log::channel(self::LOG_CHANNEL)->error('=== SUBSCRIPTION UPGRADE ROLLBACK ===', [
            'event' => 'upgrade_rollback',
            'reason' => $reason,
            'new_subscription_id' => $newSubscription->id,
            'current_subscription_id' => $currentSubscription->id,
            'rollback_actions' => [
                'new_subscription_deleted' => true,
                'current_subscription_restored' => true
            ]
        ]);
    }

    /**
     * Log general error
     */
    public static function logError(
        string $context,
        \Exception $exception,
        array $additionalData = []
    ): void {
        Log::channel(self::LOG_CHANNEL)->error("Error in {$context}", array_merge([
            'event' => 'error',
            'context' => $context,
            'error' => [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ]
        ], $additionalData));
    }
}
