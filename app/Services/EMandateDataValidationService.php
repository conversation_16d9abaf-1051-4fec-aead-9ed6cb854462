<?php

namespace App\Services;

use App\Models\User;
use App\Models\Company;
use App\Models\UserDetail;
use App\Models\Payment\BayarCashEMandate;
use Illuminate\Support\Facades\Log;

class EMandateDataValidationService
{
    /**
     * Check if user has complete e-mandate data
     * 
     * @param User $user
     * @return array
     */
    public function validateUserEMandateData(User $user): array
    {
        $company = $user->companies;
        $userDetail = $user->userDetails;
        
        $validationResults = [
            'is_complete' => true,
            'missing_fields' => [],
            'invalid_fields' => [],
            'warnings' => [],
            'data_quality_score' => 100
        ];

        // Check required user data
        $this->validateUserBasicData($user, $validationResults);
        
        // Check user details
        $this->validateUserDetailData($userDetail, $validationResults);
        
        // Check company data
        $this->validateCompanyData($company, $validationResults);
        
        // Calculate overall completeness
        $validationResults['is_complete'] = empty($validationResults['missing_fields']) && 
                                          empty($validationResults['invalid_fields']);
        
        // Calculate data quality score
        $validationResults['data_quality_score'] = $this->calculateDataQualityScore($validationResults);
        
        return $validationResults;
    }

    /**
     * Validate user basic data
     * 
     * @param User $user
     * @param array &$validationResults
     */
    protected function validateUserBasicData(User $user, array &$validationResults): void
    {
        // Check email
        if (empty($user->email)) {
            $validationResults['missing_fields'][] = 'email';
        } elseif (!filter_var($user->email, FILTER_VALIDATE_EMAIL)) {
            $validationResults['invalid_fields'][] = 'email';
        }

        // Check username/name
        if (empty($user->username) && empty($user->name)) {
            $validationResults['missing_fields'][] = 'name';
        }
    }

    /**
     * Validate user detail data
     * 
     * @param UserDetail|null $userDetail
     * @param array &$validationResults
     */
    protected function validateUserDetailData(?UserDetail $userDetail, array &$validationResults): void
    {
        if (!$userDetail) {
            $validationResults['missing_fields'][] = 'user_details';
            return;
        }

        // Check full name components
        if (empty($userDetail->first_name) && empty($userDetail->last_name)) {
            $validationResults['missing_fields'][] = 'full_name';
        }

        // Check mobile number
        if (empty($userDetail->mobile)) {
            $validationResults['missing_fields'][] = 'mobile';
        } elseif (!$this->isValidMalaysianPhoneNumber($userDetail->mobile)) {
            $validationResults['invalid_fields'][] = 'mobile';
        }

        // Check address components for better data quality
        if (empty($userDetail->address)) {
            $validationResults['warnings'][] = 'address_missing';
        }
        
        if (empty($userDetail->city)) {
            $validationResults['warnings'][] = 'city_missing';
        }
        
        if (empty($userDetail->state)) {
            $validationResults['warnings'][] = 'state_missing';
        }
        
        if (empty($userDetail->postcode)) {
            $validationResults['warnings'][] = 'postcode_missing';
        }
    }

    /**
     * Validate company data
     * 
     * @param Company|null $company
     * @param array &$validationResults
     */
    protected function validateCompanyData(?Company $company, array &$validationResults): void
    {
        if (!$company) {
            $validationResults['missing_fields'][] = 'company';
            return;
        }

        // Check IC number (for individual payers)
        if (empty($company->ic_number)) {
            $validationResults['missing_fields'][] = 'ic_number';
        } elseif (!$this->isValidMalaysianIC($company->ic_number)) {
            $validationResults['invalid_fields'][] = 'ic_number';
        }

        // Check company name
        if (empty($company->com_name)) {
            $validationResults['missing_fields'][] = 'company_name';
        }

        // Check company mobile
        if (empty($company->com_mobile)) {
            $validationResults['warnings'][] = 'company_mobile_missing';
        } elseif (!$this->isValidMalaysianPhoneNumber($company->com_mobile)) {
            $validationResults['warnings'][] = 'company_mobile_invalid';
        }

        // Check company registration number (for business payers)
        if (empty($company->com_registration_no)) {
            $validationResults['warnings'][] = 'company_registration_missing';
        }
    }

    /**
     * Validate Malaysian phone number
     *
     * @param string $phoneNumber
     * @return bool
     */
    public function isValidMalaysianPhoneNumber(string $phoneNumber): bool
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Check if it's a valid Malaysian phone number
        // Malaysian mobile: 01X-XXXXXXX (10-11 digits)
        // With country code: 601X-XXXXXXX (12-13 digits)
        
        if (strlen($cleaned) >= 10 && strlen($cleaned) <= 13) {
            // Check if it starts with valid Malaysian prefixes
            if (preg_match('/^(01[0-9]|60[1-9]|6[0-9]|[0-9])/', $cleaned)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Validate Malaysian IC number
     *
     * @param string $icNumber
     * @return bool
     */
    public function isValidMalaysianIC(string $icNumber): bool
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $icNumber);
        
        // Malaysian IC should be 12 digits
        if (strlen($cleaned) !== 12) {
            return false;
        }
        
        // Basic format validation (YYMMDD-PB-XXXX)
        $year = substr($cleaned, 0, 2);
        $month = substr($cleaned, 2, 2);
        $day = substr($cleaned, 4, 2);
        
        // Validate month (01-12)
        if ($month < 1 || $month > 12) {
            return false;
        }
        
        // Validate day (01-31)
        if ($day < 1 || $day > 31) {
            return false;
        }
        
        return true;
    }

    /**
     * Calculate data quality score
     * 
     * @param array $validationResults
     * @return int
     */
    protected function calculateDataQualityScore(array $validationResults): int
    {
        $score = 100;
        
        // Deduct points for missing required fields
        $score -= count($validationResults['missing_fields']) * 20;
        
        // Deduct points for invalid fields
        $score -= count($validationResults['invalid_fields']) * 15;
        
        // Deduct points for warnings (less critical)
        $score -= count($validationResults['warnings']) * 5;
        
        return max(0, $score);
    }

    /**
     * Get user-friendly validation messages
     * 
     * @param array $validationResults
     * @return array
     */
    public function getValidationMessages(array $validationResults): array
    {
        $messages = [];
        
        foreach ($validationResults['missing_fields'] as $field) {
            $messages[] = $this->getMissingFieldMessage($field);
        }
        
        foreach ($validationResults['invalid_fields'] as $field) {
            $messages[] = $this->getInvalidFieldMessage($field);
        }
        
        foreach ($validationResults['warnings'] as $warning) {
            $messages[] = $this->getWarningMessage($warning);
        }
        
        return $messages;
    }

    /**
     * Get missing field message
     * 
     * @param string $field
     * @return string
     */
    protected function getMissingFieldMessage(string $field): string
    {
        $messages = [
            'email' => 'Email address is required for e-mandate enrollment.',
            'name' => 'Full name is required for e-mandate enrollment.',
            'user_details' => 'User profile details are missing. Please complete your profile.',
            'full_name' => 'First name and last name are required.',
            'mobile' => 'Mobile phone number is required.',
            'company' => 'Company information is missing.',
            'ic_number' => 'IC number is required for e-mandate enrollment.',
            'company_name' => 'Company name is required.'
        ];
        
        return $messages[$field] ?? "Required field '{$field}' is missing.";
    }

    /**
     * Get invalid field message
     * 
     * @param string $field
     * @return string
     */
    protected function getInvalidFieldMessage(string $field): string
    {
        $messages = [
            'email' => 'Please provide a valid email address.',
            'mobile' => 'Please provide a valid Malaysian mobile phone number.',
            'ic_number' => 'Please provide a valid Malaysian IC number (12 digits).'
        ];
        
        return $messages[$field] ?? "Field '{$field}' contains invalid data.";
    }

    /**
     * Get warning message
     * 
     * @param string $warning
     * @return string
     */
    protected function getWarningMessage(string $warning): string
    {
        $messages = [
            'address_missing' => 'Address information would improve your profile completeness.',
            'city_missing' => 'City information would improve your profile completeness.',
            'state_missing' => 'State information would improve your profile completeness.',
            'postcode_missing' => 'Postcode information would improve your profile completeness.',
            'company_mobile_missing' => 'Company mobile number would improve your profile completeness.',
            'company_mobile_invalid' => 'Company mobile number format could be improved.',
            'company_registration_missing' => 'Company registration number would improve your profile completeness.'
        ];
        
        return $messages[$warning] ?? "Warning: {$warning}";
    }

    /**
     * Check if user can proceed with e-mandate enrollment
     * 
     * @param User $user
     * @return array
     */
    public function canProceedWithEMandate(User $user): array
    {
        $validation = $this->validateUserEMandateData($user);
        
        return [
            'can_proceed' => $validation['is_complete'],
            'validation_results' => $validation,
            'messages' => $this->getValidationMessages($validation),
            'next_action' => $validation['is_complete'] ? 'proceed' : 'collect_data'
        ];
    }
}
