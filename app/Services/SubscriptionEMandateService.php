<?php

namespace App\Services;

use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Payment\BayarCashEMandate;
use App\Models\User;
use App\Models\Company;
use App\Services\BayarCashEMandateService;
use App\Services\SubscriptionUpgradeLogger;
use Illuminate\Support\Facades\Log;
use Exception;

class SubscriptionEMandateService
{
    protected $emandateService;

    public function __construct(BayarCashEMandateService $emandateService)
    {
        $this->emandateService = $emandateService;
    }

    /**
     * Create e-mandate enrollment for subscription
     * 
     * @param Subscription $subscription
     * @return array
     */
    public function createSubscriptionEMandate(Subscription $subscription): array
    {
        try {
            Log::channel('subscription_upgrade_emandate')->info('Creating e-mandate enrollment for subscription', [
                'event' => 'emandate_creation_start',
                'subscription_id' => $subscription->id,
                'subscription_plan_id' => $subscription->subscription_plan_id,
                'company_id' => $subscription->company_id,
                'is_upgrade' => $subscription->is_upgrade ?? false,
                'upgrade_amount' => $subscription->upgrade_amount ?? null
            ]);

            // Get subscription plan details
            $subscriptionPlan = $subscription->subscriptionPlan;
            if (!$subscriptionPlan) {
                throw new Exception('Subscription plan not found');
            }

            // Get company and user details for prefilling
            $company = $subscription->company;
            $user = $company ? $company->user : null;

            if (!$company || !$user) {
                throw new Exception('Company or user not found for subscription');
            }

            // Check if user already has an active e-mandate for the same amount and frequency
            $existingEnrollment = $this->findExistingEMandate($user, $subscriptionPlan, $subscription);
            if ($existingEnrollment) {
                Log::channel('subscription_upgrade_emandate')->info('Found existing e-mandate enrollment, linking to subscription', [
                    'event' => 'existing_emandate_found',
                    'subscription_id' => $subscription->id,
                    'existing_enrollment_id' => $existingEnrollment->id,
                    'enrollment_status' => $existingEnrollment->status,
                    'enrollment_amount' => $existingEnrollment->amount,
                    'enrollment_frequency' => $existingEnrollment->frequency_mode
                ]);

                // Link existing enrollment to subscription
                $subscription->update(['emandate_enrollment_id' => $existingEnrollment->id]);

                return [
                    'success' => true,
                    'message' => 'Linked to existing e-mandate enrollment',
                    'enrollment' => $existingEnrollment,
                    'redirect_url' => null,
                    'is_existing' => true
                ];
            }

            // Prepare enrollment data with user/company prefill
            $enrollmentData = $this->prepareEnrollmentData($subscription, $subscriptionPlan, $company, $user);

            // Log enrollment attempt
            SubscriptionUpgradeLogger::logEMandateEnrollmentAttempt($subscription, $enrollmentData);

            // Create new e-mandate enrollment
            $result = $this->emandateService->createEnrollmentIntent($enrollmentData);

            if ($result['success']) {
                // Link enrollment to subscription
                $enrollmentData = $result['data'];
                $enrollmentObject = $enrollmentData['enrollment'];
                $subscription->update(['emandate_enrollment_id' => $enrollmentData['enrollment_id']]);

                Log::channel('subscription_upgrade_emandate')->info('E-mandate enrollment created and linked to subscription', [
                    'event' => 'emandate_enrollment_created',
                    'subscription_id' => $subscription->id,
                    'enrollment_id' => $enrollmentData['enrollment_id'],
                    'enrollment_url' => $enrollmentData['enrollment_url'],
                    'order_number' => $enrollmentData['order_number'],
                    'amount' => $enrollmentObject->amount,
                    'status' => $enrollmentObject->status
                ]);

                // Log the result using the dedicated logger
                SubscriptionUpgradeLogger::logEMandateEnrollmentResult($subscription, $result, $enrollmentObject);

                return [
                    'success' => true,
                    'message' => 'E-mandate enrollment created successfully',
                    'enrollment' => $enrollmentObject,
                    'redirect_url' => $enrollmentData['enrollment_url'],
                    'is_existing' => false
                ];
            } else {
                Log::channel('subscription_upgrade_emandate')->error('Failed to create e-mandate enrollment for subscription', [
                    'event' => 'emandate_enrollment_failed',
                    'subscription_id' => $subscription->id,
                    'error_message' => $result['message'],
                    'result_data' => $result
                ]);

                // Log the result using the dedicated logger
                SubscriptionUpgradeLogger::logEMandateEnrollmentResult($subscription, $result);

                return [
                    'success' => false,
                    'message' => $result['message'],
                    'enrollment' => null,
                    'redirect_url' => null
                ];
            }

        } catch (Exception $e) {
            SubscriptionUpgradeLogger::logError(
                'createSubscriptionEMandate',
                $e,
                [
                    'subscription_id' => $subscription->id,
                    'subscription_plan_id' => $subscription->subscription_plan_id,
                    'company_id' => $subscription->company_id,
                    'is_upgrade' => $subscription->is_upgrade ?? false
                ]
            );

            return [
                'success' => false,
                'message' => 'Failed to create e-mandate enrollment: ' . $e->getMessage(),
                'enrollment' => null,
                'redirect_url' => null
            ];
        }
    }

    /**
     * Find existing e-mandate enrollment for user with same amount and frequency
     *
     * @param User $user
     * @param SubscriptionPlan $subscriptionPlan
     * @param Subscription $subscription
     * @return BayarCashEMandate|null
     */
    protected function findExistingEMandate(User $user, SubscriptionPlan $subscriptionPlan, Subscription $subscription = null): ?BayarCashEMandate
    {
        $frequency = $this->mapBillingFrequencyToEMandate($subscriptionPlan->billing_frequency);

        // For upgrades, use the upgrade amount; for new subscriptions, use plan price
        $amount = $subscriptionPlan->price;
        if ($subscription && isset($subscription->is_upgrade) && $subscription->is_upgrade && isset($subscription->upgrade_amount)) {
            $amount = $subscription->upgrade_amount;
        }

        return BayarCashEMandate::where('user_id', $user->id)
            ->where('amount', $amount)
            ->where('frequency_mode', $frequency)
            ->whereIn('status', [
                BayarCashEMandate::STATUS_ACTIVE,
                BayarCashEMandate::STATUS_NEW,
                BayarCashEMandate::STATUS_WAITING_APPROVAL,
                BayarCashEMandate::STATUS_APPROVED
            ])
            ->latest()
            ->first();
    }

    /**
     * Prepare enrollment data with user/company prefill
     * 
     * @param Subscription $subscription
     * @param SubscriptionPlan $subscriptionPlan
     * @param Company $company
     * @param User $user
     * @return array
     */
    protected function prepareEnrollmentData(Subscription $subscription, SubscriptionPlan $subscriptionPlan, Company $company, User $user): array
    {
        // Map billing frequency to e-mandate frequency
        $frequency = $this->mapBillingFrequencyToEMandate($subscriptionPlan->billing_frequency);

        // Get user details for prefilling
        $userDetail = $user->userDetails;
        
        // Determine if this is an upgrade and adjust amount accordingly
        $amount = $subscriptionPlan->price;
        $applicationReason = "Subscription for {$subscriptionPlan->name}";
        $orderDescription = "Subscription: {$subscriptionPlan->name}";

        if (isset($subscription->is_upgrade) && $subscription->is_upgrade && isset($subscription->upgrade_amount)) {
            $amount = $subscription->upgrade_amount;
            $applicationReason = "Subscription upgrade to {$subscriptionPlan->name}";
            $orderDescription = "Subscription Upgrade: {$subscriptionPlan->name}";
        }

        return [
            'user_id' => $user->id,
            'company_id' => $company->id,
            'amount' => $amount,
            'frequency_mode' => $frequency,
            'payer_id_type' => $this->determinePayerIdType($userDetail),
            'payer_id' => $this->getPayerId($userDetail, $company),
            'payer_name' => $this->getPayerName($userDetail, $user),
            'payer_email' => $user->email,
            'payer_telephone_number' => $this->getPhoneNumber($userDetail, $company),
            'application_reason' => $applicationReason,
            'order_description' => $orderDescription,
            'effective_date' => now()->addDays(7)->format('Y-m-d'), // 7 days from now
            'metadata' => [
                'subscription_id' => $subscription->id,
                'subscription_plan_id' => $subscriptionPlan->id,
                'source' => isset($subscription->is_upgrade) && $subscription->is_upgrade ? 'subscription_upgrade' : 'subscription_payment',
                'is_upgrade' => isset($subscription->is_upgrade) ? $subscription->is_upgrade : false,
                'upgrade_amount' => isset($subscription->upgrade_amount) ? $subscription->upgrade_amount : null
            ]
        ];
    }

    /**
     * Map subscription billing frequency to e-mandate frequency
     * 
     * @param string $billingFrequency
     * @return string
     */
    protected function mapBillingFrequencyToEMandate(string $billingFrequency): string
    {
        switch (strtolower($billingFrequency)) {
            case 'monthly':
                return BayarCashEMandate::FREQUENCY_MONTHLY; // MT
            case 'yearly':
            case 'annual':
                return BayarCashEMandate::FREQUENCY_YEARLY; // YR
            case 'weekly':
                return BayarCashEMandate::FREQUENCY_WEEKLY; // WK
            case 'daily':
                return BayarCashEMandate::FREQUENCY_DAILY; // DL
            default:
                Log::warning('Unknown billing frequency, defaulting to monthly', [
                    'billing_frequency' => $billingFrequency
                ]);
                return BayarCashEMandate::FREQUENCY_MONTHLY; // Default to monthly
        }
    }

    /**
     * Determine payer ID type based on user details
     * 
     * @param mixed $userDetail
     * @return int
     */
    protected function determinePayerIdType($userDetail): int
    {
        // Default to NRIC if no specific logic needed
        return BayarCashEMandate::PAYER_ID_TYPE_NRIC;
    }

    /**
     * Get payer ID from user details or company
     * 
     * @param mixed $userDetail
     * @param Company $company
     * @return string
     */
    protected function getPayerId($userDetail, Company $company): string
    {
        // Try to get IC number from user details first
        if ($userDetail && isset($company->ic_number)) {
            return $company->ic_number;
        }

        // Fallback to company registration number or generate placeholder
        if ($company && isset($company->registration_number)) {
            return $company->registration_number;
        }

        // Generate a placeholder ID (this should be replaced with actual user input)
        return '000000000000';
    }

    /**
     * Get payer name from user details
     * 
     * @param mixed $userDetail
     * @param User $user
     * @return string
     */
    protected function getPayerName($userDetail, User $user): string
    {
        if ($userDetail) {
            // Try to build full name from first_name and last_name
            $firstName = $userDetail->first_name ?? '';
            $lastName = $userDetail->last_name ?? '';

            if (!empty($firstName) || !empty($lastName)) {
                return trim($firstName . ' ' . $lastName);
            }

            // Fallback to full_name field if it exists
            if (isset($userDetail->full_name) && !empty($userDetail->full_name)) {
                return $userDetail->full_name;
            }
        }

        // Fallback to user name or username
        return $user->name ?? $user->username ?? 'Unknown User';
    }

    /**
     * Get phone number from user details or company
     * 
     * @param mixed $userDetail
     * @param Company $company
     * @return string
     */
    protected function getPhoneNumber($userDetail, Company $company): string
    {
        // Try user detail mobile first
        if ($userDetail && isset($userDetail->mobile) && !empty($userDetail->mobile)) {
            return $userDetail->mobile;
        }

        // Fallback to phone_number field if it exists
        if ($userDetail && isset($userDetail->phone_number) && !empty($userDetail->phone_number)) {
            return $userDetail->phone_number;
        }

        // Try company mobile
        if ($company && isset($company->com_mobile) && !empty($company->com_mobile)) {
            return $company->com_mobile;
        }

        // Fallback to company phone_number field if it exists
        if ($company && isset($company->phone_number) && !empty($company->phone_number)) {
            return $company->phone_number;
        }

        // Default placeholder (should not reach here with proper validation)
        return '0123456789';
    }

    /**
     * Handle e-mandate enrollment status for subscription
     * 
     * @param Subscription $subscription
     * @param int $status
     * @return array
     */
    public function handleEMandateStatus(Subscription $subscription, int $status): array
    {
        $statusInterpretation = BayarCashEMandate::getSdkStatusInterpretation($status);

        Log::info('Handling e-mandate status for subscription', [
            'subscription_id' => $subscription->id,
            'status' => $status,
            'interpretation' => $statusInterpretation
        ]);

        if ($statusInterpretation['is_pending']) {
            // Status 0 (New/Pending) - Allow continued service usage
            return [
                'success' => true,
                'message' => 'Your Direct Debit enrollment is now in process. You may continue to use our services.',
                'status' => 'pending',
                'allow_service_usage' => true
            ];
        } elseif ($statusInterpretation['is_success']) {
            // Status 3 (Active) or 5 (Approved) - Activate subscription
            $subscription->update(['status' => 'active']);
            
            return [
                'success' => true,
                'message' => 'Your Direct Debit has been successfully set up and your subscription is now active.',
                'status' => 'active',
                'allow_service_usage' => true
            ];
        } else {
            // Failure statuses
            return [
                'success' => false,
                'message' => 'Direct Debit setup failed: ' . $statusInterpretation['description'],
                'status' => 'failed',
                'allow_service_usage' => false,
                'failure_causes' => $this->getFailureCauses($status)
            ];
        }
    }

    /**
     * Get possible failure causes for failed e-mandate enrollment
     * 
     * @param int $status
     * @return array
     */
    protected function getFailureCauses(int $status): array
    {
        switch ($status) {
            case BayarCashEMandate::SDK_STATUS_FAILED_BANK_VERIFICATION:
                return [
                    'Bank account verification failed',
                    'Insufficient funds in account',
                    'Account is not eligible for direct debit',
                    'Bank account details are incorrect'
                ];
            case BayarCashEMandate::SDK_STATUS_REJECTED:
                return [
                    'Bank rejected the direct debit request',
                    'Account holder name mismatch',
                    'Account is closed or suspended',
                    'Bank does not support FPX direct debit'
                ];
            case BayarCashEMandate::SDK_STATUS_ERROR:
                return [
                    'Technical error occurred',
                    'Bank system temporarily unavailable',
                    'Network connectivity issues',
                    'Please try again later'
                ];
            default:
                return [
                    'Unknown error occurred',
                    'Please contact customer support',
                    'Try using alternative payment method'
                ];
        }
    }
}
