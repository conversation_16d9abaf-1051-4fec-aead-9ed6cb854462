<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckBayarCashConfig
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if BayarCash configuration is complete
        $apiToken = config('services.bayarcash.api_token');
        $apiSecretKey = config('services.bayarcash.api_secret_key');
        $portalKey = config('services.bayarcash.portal_key');

        $missingConfig = [];

        if (empty($apiToken)) {
            $missingConfig[] = 'BC_API_TOKEN';
        }

        if (empty($apiSecretKey)) {
            $missingConfig[] = 'BC_API_SECRET_KEY';
        }

        if (empty($portalKey)) {
            $missingConfig[] = 'BC_PORTAL_KEY';
        }

        if (!empty($missingConfig)) {
            Log::error('BayarCash configuration incomplete - blocking payment request', [
                'missing_config' => $missingConfig,
                'route' => $request->route()?->getName(),
                'url' => $request->url(),
                'user_id' => auth()->id(),
                'environment' => app()->environment()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment service is temporarily unavailable. Please try again later or contact support.',
                    'error_code' => 'PAYMENT_CONFIG_ERROR'
                ], 503);
            }

            return redirect()->back()->withErrors([
                'payment' => 'Payment service is temporarily unavailable. Please try again later or contact support.'
            ]);
        }

        return $next($request);
    }
}
