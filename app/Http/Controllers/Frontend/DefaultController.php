<?php

namespace App\Http\Controllers\Frontend;

use App\Models\User;
use App\Models\Company;
use App\Models\UserDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;

class DefaultController extends Controller
{
    public function privacyPolicy()
    {
        return view('frontend.privacy.template');
    }

    // DEPRECATED
    public function loginCheckModal(Request $request)
    {
        $request->validate([
            'domain' => 'required',
            'username' => 'required',
            'password' => 'required',
        ]);
        $response = request()->all();

        $urlPos = config('bizappos.bizappos_api_url');
        $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
            'DOMAIN' => $response['domain'],
            'username' => $response['username'],
            'password' => $response['password'],
            'platform' => 'POS'
        ])->throw()->json();

        if ($bizappLogin[0]['STATUS'] == '0'){
            return response()->json(['errors' => 'Invalid Credentials']);
        }   else if ($bizappLogin[0]['roleid'] == '4'){ // if not HQ account
            return response()->json(['errors' => "It seems that you are trying to upgrade on a 'STAFF' account. Please login from a 'HQ' account before proceeding."]);
        } else if ($bizappLogin[0]['roleid'] != '3') { // if not ultimate
            return response()->json(['errors' => "You need at least an Bizapp ULTIMATE account to proceed. Please upgrade your account first."]);
        } else {
            // run another API for pre-filled data
            $prefilled = Http::asForm()->post($urlPos . 'api_name=TRACK_GETUSER_INFO_BIZAPPOS', [
                'pid' => $bizappLogin[0]['pid'],
                'DOMAIN' => $response['domain']
            ])->throw()->json();

            // check if user is already registered in web.bizappos DB
            $user = User::where('pid',$bizappLogin[0]['pid'])->first();
            if(!$user){
                $user = User::create([
                    'username' => $bizappLogin[0]['penggunaid'],
                    'password' => bcrypt($bizappLogin[0]['katalaluan']),
                    'email' => $bizappLogin[0]['emel'],
                    'isBizappUser' => 'Y',
                    'domain' => $request->domain,
                    'pid' => $bizappLogin[0]['pid']
                ]);
                $user->save();
    
                $userDetail = UserDetail::updateOrCreate([
                    'user_id' => $user->id,
                    'first_name' => $bizappLogin[0]['nama'],
                    'mobile' => $bizappLogin[0]['nohp'],
                    'avatar' => $bizappLogin[0]['attachmentphoto'],
                    'address' => $bizappLogin[0]['alamat1'] . ', ' . $bizappLogin[0]['alamat2'] . ', ' . $bizappLogin[0]['alamat3'],
                    'currency' => $bizappLogin[0]['currency'],
                    'bizapp_secretkey' => $bizappLogin[0]['secretkey'],
                    'state' => $bizappLogin[0]['negeri'],
                    'postcode' => $bizappLogin[0]['poskod'],
                    'country' => $bizappLogin[0]['country']
                ]);
                $userDetail->save();
                
                if ($bizappLogin[0]['roleid'] == "3"){
                    $company = Company::create([
                        'user_id' => $user->id,
                        'com_name' => $bizappLogin[0]['namaboss'],
                        'com_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
                        'com_state' => $bizappLogin[0]['negeriboss'],
                        'com_postcode' => $bizappLogin[0]['poskodboss'],
                        'account_type' => 'company',
                        'com_mobile' => $bizappLogin[0]['nohpboss'],
                        'com_email' => $bizappLogin[0]['emelboss'],
                        'com_country' => $bizappLogin[0]['country']
                    ]);
                    $company->save();
                } 
            }
            Session::put('prefilled', $prefilled[0]);
            Session::put('loginData', $bizappLogin[0]);

            return response()->json(['url' => redirect('upgrade-form')->getTargetUrl()]);
       
        }
    }

    // DEPRECATED
    // public function upgradeForm(Request $request)
    // {
    //     $response = request()->all();

    //     // dd($response);
    //     // check if the user is in the database
    //     $pid = $response['pid']; 
    //     $findPid = User::where('pid',$pid)->first();
    //     if ($findPid == null){
    //             // login with bizapp credentials
    //             $urlPos = config('bizappos.bizappos_api_url');
    //             $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
    //                 'DOMAIN' => $response['loginDomain'],
    //                 'username' => $response['loginUsername'],
    //                 'password' => $response['loginPassword'],
    //                 'platform' => 'POS'
    //             ])->throw()->json();
                        
    //             $user = User::create([
    //                 'username' => $bizappLogin[0]['penggunaid'],
    //                 'password' => bcrypt($bizappLogin[0]['katalaluan']),
    //                 'email' => $bizappLogin[0]['emel'],
    //                 'isBizappUser' => 'Y',
    //                 'domain' => $response['loginDomain'],
    //                 'pid' => $bizappLogin[0]['pid']
    //             ]);
    //             $user->save();
    
    //             $userDetail = UserDetail::create([
    //                 'user_id' => $user->id,
    //                 'first_name' => $bizappLogin[0]['nama'],
    //                 'mobile' => $bizappLogin[0]['nohp'],
    //                 'avatar' => $bizappLogin[0]['attachmentphoto'],
    //                 'address' => $bizappLogin[0]['alamat1'] . ', ' . $bizappLogin[0]['alamat2'] . ', ' . $bizappLogin[0]['alamat3'],
                    
    //                 'state' => $bizappLogin[0]['negeri'],
    //                 'postcode' => $bizappLogin[0]['poskod'],
    //                 'country' => $bizappLogin[0]['country']
    //             ]);
    //             $userDetail->save();
                
    //             $company = Company::create([
    //                 'user_id' => $user->id,
    //                 'com_name' => $bizappLogin[0]['namaboss'],
    //                 'com_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
        
    //                 'com_state' => $bizappLogin[0]['negeriboss'],
    //                 'com_postcode' => $bizappLogin[0]['poskodboss'],
    //                 'com_mobile' => $bizappLogin[0]['nohpboss'],
    //                 'com_email' => $bizappLogin[0]['emelboss'],
    //                 'com_country' => $bizappLogin[0]['country']
    //             ]);
    //             $company->save();

                    
    //             // check if user role is staff
    //             if ($bizappLogin[0]['roleid'] == "4"){
    //                 $body = ['DOMAIN' => $request->domain, 'pid' => $bizappLogin[0]['pid'], 'TOKEN' => 'aa'];

    //                 $bizappAccessModule = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_MODULE_ACCESS_BYPID', $body);

    //                 $thisUser = User::find($findUserLocal->id);
    //                 $thisUser->access_module = $bizappAccessModule->body();
    //                 $thisUser->save();
    //             }
    //         }
    //         if($request->hasFile('ssmDoc')){
    //             // Retrieve the PDF file from the request
    //             $ssmCert = $request->file('ssmDoc');
    //             // Generate a unique filename
    //             $filename =  $response['companyName'] . '/' . 'ssm' . '.' . $ssmCert->getClientOriginalExtension();

    //             // Store the PDF file in the storage directory
    //             $ssmCert->storeAs('pdfs', $filename, 'public'); // 'public' is the storage disk name
    //             $document1Path = 'pdfs/' . $filename;
    //         }
    //         if($request->hasFile('nricDoc')){
    //             $nricDoc = $request->file('nricDoc');

    //             $filename =  $response['companyName'] . '/' . 'nric' . '.' . $nricDoc->getClientOriginalExtension();

    //             $nricDoc->storeAs('pdfs', $filename, 'public');
    //             $document2Path = 'pdfs/' . $filename;
    //         }
    //         if($request->hasFile('bankDoc')){
    //             $bankStatement = $request->file('bankDoc');

    //             $filename = $response['companyName'] . '/' . 'bankStmt' . '.' . $bankStatement->getClientOriginalExtension();

    //             $bankStatement->storeAs('pdfs', $filename, 'public'); 
    //             $document3Path = 'pdfs/' . $filename;
    //         }
  
    //         if($request->hasFile('photoDoc')){
    //             $premisePhoto = $request->file('photoDoc');

    //             $filename = $response['companyName'] . '/' . 'prmsPhoto' . '.' . $premisePhoto->getClientOriginalExtension();

    //             $premisePhoto->storeAs('pdfs', $filename, 'public'); 
    //             $document4Path = 'pdfs/' . $filename;
    //         }
    
    //         if($request->hasFile('otherDoc')) {
    //             $otherDoc = $request->file('otherDoc');

    //             $filename = $response['companyName'] . '/' . 'otherDoc' . '.' . $otherDoc->getClientOriginalExtension();

    //             $otherDoc->storeAs('pdfs', $filename, 'public');
    //             $document5Path = 'pdfs/' . $filename;
    //         }
    //         if($request->has('signature')) {
    //             // $signatureFile = $request->file('signature');
    //             // $signatureFile = str_replace('data:image/png;base64,', '', $signatureFile);
    //             // $signatureFile = str_replace(' ', '+', $signatureFile);
    //             // $signatureFile = base64_decode($signatureFile);

    //             // $signtureFilename = $response['companyName'] . '/' . 'signature' . '.' . $signatureFile->getClientOriginalExtension();
            
    //             // $signatureFile->storeAs('signature', $signtureFilename, 'public');
    //             // $signatureFilePath = 'signature/' . $signtureFilename;

    //             $signatureData = str_replace('data:image/png;base64,', '', $response['signature']);
    //             $signatureData = str_replace(' ', '+', $signatureData);
    //             $signatureData = base64_decode($signatureData);

    //             $folderPath = 'signature/' . $response['companyName'];
    //             $filename = 'signature.png';
                
    //             Storage::disk('public')->put($folderPath . '/' . $filename, $signatureData);
                
    //             $signatureFilePath = $folderPath . '/' . $filename;
    //         }

    //         $findPid2 = User::where('pid',$pid)->first(); // find the user again
    //         $saveRegistration = GkashRegistration::updateOrCreate(
    //             [
    //                 'user_id' => $findPid2->id
    //             ],
    //             [
    //             'companyName' => $response['companyName'],
    //             'companySSM' => $response['ssmNumber'],
    //             'companyDBA' => $response['traderName'],
    //             'companyEmail' => $response['email'],
    //             'companyAdd1' => $response['addressL1'],
    //             'companyAdd2' => $response['addressL2'] ?? '',
    //             'companyAdd3' => $response['addressL3'] ?? '',
    //             'companyPostcode' => $response['postcode'],
    //             'companyCity' => $response['city'],
    //             'companyState' => $response['state'],
    //             'companyCountry' => 'MALAYSIA',
    //             'businessType' => $response['businessType'],
    //             'businessNature' => $response['businessSector'],
    //             'businessProductPrice' => $response['priceRange'],
    //             'businessAMS' => $response['ams'],
    //             'contactPersonName' => $response['name'],
    //             'contactPersonNRIC' => $response['nric'],
    //             'contactPersonPosition' => $response['position'],
    //             'contactPersonPhone' => $response['contact'],
    //             'companyBankName' => $response['bankName'],
    //             'companyAccountName' => $response['bankAccountHolder'],
    //             'companyAccountNumber' => $response['bankAccountNumber'],
    //             'pepName' => $response['pepName'] ?? null,
    //             'pepPosition' => $response['pepPosition'] ?? null,
    //             'pepRelationship' => $response['pepRelationship'] ?? null,
    //             'documentSSM' => $document1Path ?? null,
    //             'documentNRIC' => $document2Path ?? null,
    //             'documentBankStatement' => $document3Path ?? null,
    //             'documentPoB' => $document4Path ?? null,
    //             'documentOthers' => $document5Path ?? null,
    //             'signatureFile' => $signatureFilePath ?? null,
    //         ]);
    //         $saveRegistration->save();

    //         // BIZAPPAY - GET TOKEN
    //         $bizappayToken = (new BizappayController)->generateToken();
            
    //         // BIZAPPAY - GENERATE BILL
    //         $getApiKey = config('services.bizappay.key');
    //         $bizappayUrl = config('services.bizappay.url');
    //         $bizappayCategoryCode = config('services.bizappay.categoryCode');

    //         // BIZAPPAY - SANDBOX
    //         $getApiKeySandbox = config('services.bizappay.keySandbox');
    //         $bizappayUrlSandbox = config('services.bizappay.urlSandbox');
    //         $bizappayCategoryCodeSandbox = config('services.bizappay.categoryCodeSandbox');

    //         $bizappayPage = Http::withHeaders([
    //             'Authentication' => $bizappayToken,
    //         ])
    //         ->asForm()
    //         ->post($bizappayUrl . 'bill/create', [
    //             'apiKey' => $getApiKey,
    //             'category' => $bizappayCategoryCode, //  4z6h3kuf <-- staging code || e2audfn6 <-- production code
    //             'name' => 'Bizappos PLUS Registration',
    //             'amount' => '413.40',
    //             'payer_name' => $response['name'],
    //             'payer_email' => $response['email'],
    //             'payer_phone' => $response['contact'],
    //             'webreturn_url' => route('payment.done'),
    //             'callback_url' => route('api.payment.done'),
    //         ])->throw()->json();
        

    //         if($bizappayPage['status'] == "ok"){
    //             $updateBillcode = GkashRegistration::where('user_id',$saveRegistration->user_id)->first();
    //             $updateBillcode->billcode = $bizappayPage['billCode'];
    //             $updateBillcode->save();

    //             $updateBizappayTable = new Bizappay([
    //                 'billcode' => $bizappayPage['billCode'],
    //                 'categorycode' => $bizappayCategoryCode, // 4z6h3kuf <-- staging code  || e2audfn6 <-- production code
    //                 'paidamount' => '413.40',
    //                 'paymentstatus' => 'Pending'
    //             ]);
    //             $updateBizappayTable->save();
    //         }

    //         if ($saveRegistration->exists) {
    //             return redirect($bizappayPage['url']);
    //          } else {
    //             return response()->json([
    //                 'success' => false
    //             ]);
    //          }
    // }

    public function processSignature(Request $request)
    {
        // dd($request->has('signature'));
        // $request->signature;

        try {
            if($request->has('signature')) {

                $signatureData = str_replace('data:image/png;base64,', '', $request->signature);
                $signatureData = str_replace(' ', '+', $signatureData);
                $signatureData = base64_decode($signatureData);
    
                $folderPath = 'signature/' . date('Y-m-d_H-i-s');
                $filename = 'signature.png';
                
                Storage::disk('public')->put($folderPath . '/' . $filename, $signatureData);
                
                $signatureFilePath = $folderPath . '/' . $filename;
    
                return redirect()->back()->with('success','Your signature has been saved successfully, you may close this window.');
            }
        } catch (\Exception $e) {
            Log::info("signature failed : {$e->getMessage()}");
            return redirect()->back()->with('error','Your signature was not saved, please contact Admin');
        }
        
    }
}
