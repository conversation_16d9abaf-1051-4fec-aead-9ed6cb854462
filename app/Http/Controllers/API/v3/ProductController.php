<?php

namespace App\Http\Controllers\API\v3;

use App\Models\Product;
use App\Models\Collection;
use Illuminate\Http\Request;
use App\Models\ProductDetail;
use App\Models\CollectionProduct;
use App\Jobs\SyncProductBizappJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use App\Jobs\SyncProductCollectionJob;
use Illuminate\Support\Facades\Validator;

class ProductController extends Controller
{
    public function index(Request $request){
        $user = getUser();
        if(!$user){
            return $this->sendResponseV3(false, "0" ,"Please log in to continue");
        }

        $query = Product::query();
        $query->with('productDetail','category');
        $query->where('parent_company', $user['company']['id']);
        $query->where('product_status', '1');

        // search
        $query->when($request->search, function($q) use ($request){
            $q->where(function($q) use ($request){
                $search = "%{$request->search}%";
                $q->where('product_name', 'like', $search);
                $q->orWhere('product_SKU', 'like', $search);
            });
        });
        // price range
        $query->when($request->min_price || $request->max_price, function($q) use ($request){
            $q->where(function($q) use ($request){
                $minPrice = $request->min_price;
                $maxPrice = $request->max_price;
                $q->whereBetween('product_price', [
                    $minPrice ?? 0,
                    $maxPrice ?? 100000
                ]);
            });
        });

        $query->when($request->in_stock, function($q) use ($request){
            $q->where(function($q) use ($request){
                $q->where('product_stock', '!=' , 0);
            });
        });

        $query->when($request->sort_by, function ($q) use ($request) {
            // User-defined sort
            switch ($request->sort_by) {
                case 'price_asc':
                    $q->orderBy('product_price', 'asc');
                    break;
                case 'price_desc':
                    $q->orderBy('product_price', 'desc');
                    break;
                case 'newest':
                    $q->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $q->orderBy('created_at', 'asc');
                    break;
                default:
                    // If sort_by is invalid, use default sorting
                    $q->orderByRaw('CASE WHEN product_fav = true THEN 0 ELSE 1 END, created_at DESC');
                    break;
            }
        }, function ($q) {
            // Default sort if no sort_by is provided
            $q->orderByRaw('CASE WHEN product_fav = true THEN 0 ELSE 1 END, product_name ASC');
        });

        // $query->orderByRaw('CASE WHEN product_fav = true THEN 0 ELSE 1 END, created_at DESC');
        $list = $query->paginate($request->show ?? 6000);

        $array = [];
        foreach($list as $item){
            $array[] = [
                'id' => $item->id,
                'bizapp_id' => $item->product_id_bizapp,
                'name' => $item->product_name,
                'sku' => $item->product_SKU,
                'description' => $item->product_description,
                'note' => $item->product_note,
                'brand' => $item->product_brand,
                'category' => $item->category?->category_name,
                'price' => $item->product_price,
                'stock' => $item->product_stock,
                'cost_price' => $item->cost_price,
                'product_weight' => $item->product_weight,
                'product_fav' => ($item->product_fav == "1") ? "1" : "0",
                'created_at' => $item->created_at->format('d/m/Y'),
                'updated_at' => $item->updated_at->format('d/m/Y H:i:s'),
                'product_attachment' => $item->productDetail?->product_attachment
            ];
        }
        if(is_array($array) && empty($array)    ){
            return $this->sendResponseV3(false,"0",$request->search . " not found");
        }
        $data = [
            'list' => $array,
            'pagination' => [
                'total' => $list->total(),
                'per_page' => $list->perPage(),
                'current_page' => $list->currentPage(),
                'last_page' => $list->lastPage(),
                'from' => $list->firstItem(),
                'to' => $list->lastItem()
            ]
        ];

        // function to sync bizapp products if the user is from bizapp
        // if(isBizappUserCheck()){
        //     $userData = User::where('pid',$user['pid'])->first();
        //     SyncProductBizappJob::dispatch($userData)->onQueue('syncProduct');
        // }
        return $this->sendResponseV3(true, "1", "Success", $data);
    }

    public function getAllProduct(Request $request){
        $user = getUser();
        if(!$user){
            return $this->sendResponseV3(false, "0" ,"Please log in to continue");
        }

        $query = Product::query();
        $query->with('productDetail','category');
        $query->where('parent_company', $user['company']['id']);
        $query->where('product_status', '1');
        $query->orderByRaw('CASE WHEN product_fav = true THEN 0 ELSE 1 END, created_at DESC');
        $products = $query->get();
        $array = [];
        foreach($products as $item){
            $array[] = [
                'id' => $item->id,
                'bizapp_id' => $item->product_id_bizapp,
                'name' => $item->product_name,
                'sku' => $item->product_SKU,
                'description' => $item->product_description,
                'note' => $item->product_note,
                'brand' => $item->product_brand,
                'category' => $item->category?->category_name,
                'price' => $item->product_price,
                'stock' => $item->product_stock,
                'cost_price' => $item->cost_price,
                'product_weight' => $item->product_weight,
                'product_fav' => ($item->product_fav == "1") ? "1" : "0",
                'created_at' => $item->created_at->format('d/m/Y'),
                'updated_at' => $item->updated_at->format('d/m/Y H:i:s'),
                'product_attachment' => $item->productDetail?->product_attachment
            ];
        }
        // if(is_array($array) && empty($array)    ){
        //     return $this->sendResponseV3(false,"0","Error 404 : not found");
        // }
        $data = [
            'list' => $array,
        ];

        return $this->sendResponseV3(true, "1", "Success", $data);

    }
    public function addNewProduct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'productSKU' => 'required|string|max:50',
            'productName' => 'required|string',
            'productBrand' => 'required|string',
            'productDescription' => 'required|string',
            'productPrice' => 'required|decimal:0,2',
            'productInventory' => 'required|string',
            'main-image' => 'file|mimes:png,jpg,jpeg,heic|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => '0',
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try{
            DB::beginTransaction();
                $product = Product::create([
                    'product_brand' => $request->productBrand ?? 'TIADA BRAND',
                    'product_SKU' => $request->productSKU,
                    'product_name' => $request->productName,
                    'product_stock' => $request->productInventory == "Ready Stock" ? '-100' : $request->productInventory,
                    'product_stock_status' => $request->productInventory == "Ready Stock" ? 'Y' : 'N',
                    'product_price' => $request->productPrice,
                    'cost_price' => $request->costPrice ?? null,
                    'product_weight' => $request->productWeight ?? 0,
                    'product_fav' => '0',
                    'product_description' => $request->productDescription,
                    'parent_company' => auth()->user()->companies->id ?? auth()->user()->employee->parent_company,
                    'category_id' => $category->id ?? null
                ]);
                $prodDetails = ProductDetail::create([
                    // 'category_id' => $userProductCategory->id,
                    'product_id' => $product->id,
                    'product_attachment' => null
                ]);

            if(auth()->user()->isBizappUser === 'Y' && app()->environment('production')){ // update the product in BIZAPP to sync
                try {
                    $urlPos = config('bizappos.bizappos_api_url');
                    $urlPos123 = config('bizappos.bizappos_api_url_test');
                    $addProductToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDPRODUCT&TX=',[
                        'pid' => auth()->user()->pid,
                        'token' => 'aa',
                        'DOMAIN' => auth()->user()->domain,
                        'productname' => $request->productName,
                        'sku' => $request->productSKU,
                        'price' => $request->productPrice,
                        'costprice' => $request->costPrice,
                        'stoktidakterhad' => $request->productInventory === "Ready Stock" ? 'Y' : 'N',
                        'bilstok' => $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                        // 'productcategory' => $userProductCategory->bizapp_code,
                        'productcategory' => $category->bizapp_code ?? null,
                        'productbrand' => $request->productBrand
                    ])->throw()->json();
                    Log::info("Store product to bizapp endpoint : " . ($urlPos123 . 'api_name=TRACK_ADDPRODUCT&TX='));
                    Log::info("Store product to bizapp --stoktidakterhad : " . ($request->productInventory === "Ready Stock" ? 'Y' : 'N'));
                    Log::info("Store product to bizapp --bilstok : " . ($request->productInventory === "Ready Stock" ? '-100' : $request->productInventory));
                    // Prepare and send the API request to Bizapp
                    if($addProductToBizapp){
                        $product->product_id_bizapp = $addProductToBizapp;
                        $product->save();

                        if($request->file('main-image')){

                            Http::attach(
                                'file', file_get_contents($request->file('main-image')->getRealPath()),
                                $request->file('main-image')->getClientOriginalName()
                            )->asMultipart()->post($urlPos123 . 'api_name=TRACK_SAVE_PRODUCT_ATTACHMENT&TX=', [
                                'pid' => auth()->user()->pid,
                                'TOKEN' => 'toke',
                                'DOMAIN' => auth()->user()->domain,
                                'productid' => $addProductToBizapp,
                            ])->throw()->json();
                        }
                        // $response as in saving attachments does not return anything, make another API call for product detail based

                        if($request->productInventory === "Ready Stock"){ // product is ready stock
                            $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'TRACK_READYSTOCKINVENTORI&TX=',[
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok'=> '0',
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => '0',
                                'productid' => $addProductToBizapp,
                            ])->throw()->json();
                        } else {
                            $updateStockToBizapp = Http::asForm()->post($urlPos123 . 'api_name=TRACK_ADDINVENTORI&TX=',[
                                'pid' => auth()->user()->pid,
                                'domain' => auth()->user()->domain,
                                'TOKEN' => 'tke',
                                'bilstok'=> $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                                'nota' => 'Update stock count in bizappos back-office',
                                'balance' => $request->productInventory === "Ready Stock" ? '-100' : $request->productInventory,
                                'productid' => $addProductToBizapp,
                            ])->throw()->json();
                        }

                        Log::info("Store product to bizapp 2 -- bilstok : " . ($request->productInventory === "Ready Stock" ? '-100' : $request->productInventory));
                        Log::info("Store product to bizapp 2 -- balance : " . ($request->productInventory === "Ready Stock" ? '-100' : $request->productInventory));

                        $getDetailFromBizapp = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                        'pid' => auth()->user()->pid,
                        'token' => 'aa',
                        'DOMAIN' => auth()->user()->domain,
                        'productid' => $addProductToBizapp
                        ])->throw()->json();

                        if(!empty($getDetailFromBizapp) && $getDetailFromBizapp[0]['STATUS'] === '1'){
                            $prodDetails->product_attachment = $getDetailFromBizapp[0]['attachment'];
                            $prodDetails->save();
                        }
                    }

                } catch (\Exception $e) {
                    Log::error('BizApp API error: ' . $e->getMessage());
                    // return redirect()->back()->with('error', 'Error updating product in BizApp');
                    return $this->sendResponseV3(false, "0", "Error updating product in Bizapp",$product,200);

                }

            }

            DB::commit();
            // return redirect()->route('products')->with('success', 'Product inserted successfully');
            return $this->sendResponseV3(true, "1", "Product inserted successfully",$product,200);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('update product error : ' . $e->getMessage());
            return $this->sendResponseV3(false, "0", "Something is wrong, please contact admin",[],400);
            // return redirect()->back()->withError('Something is wrong, please contact admin');
        }

    }
    public function getProductDetails(Request $request)
    {
        $prodID = $request->input('product_id');

        $user = getUser();
        $product = Product::with('productDetail')
                    ->where('parent_company',$user['company']['id'])
                    ->where('product_id',$prodID)
                    ->first();

        if($product){
            return $this->sendResponseV3(true,'1','success',$product,200);
        } else {
            return $this->sendResponseV3(false,'0','success',$product,400);
        }
    }

    public function setFavorite(Request $request){
        $validator = Validator::make($request->all(), [
            'id' => 'required|string'
        ]);

        if($validator->fails()){
            return $this->sendResponseV3(false, "0", $validator->errors()->first(), 400);
        }

        $user = getUser();
        if(!$user){
            return $this->sendResponseV3(false, "0", "Please login first", 401);
        }


        $product = Product::where([
            'id' => $request->id,
            'parent_company' => $user['company']['id'] ?? null
        ])->first();
        if(!$product){
            return $this->sendResponseV3(false, "0", "Product not found", 404);
        }

        if (isBizappUserCheck()){
            $urlPos = config('bizappos.bizappos_api_url');
            $setFav = Http::asForm()->post($urlPos . 'api_name=TRACK_SETFAV&TX=',[
                'pid' => auth()->user()->pid,
                'TOKEN' => 'aa',
                'DOMAIN' => auth()->user()->domain,
                'productid' => $product->product_id_bizapp
            ])->throw()->json();
        }

        $product->update([
            'product_fav' => ($product->product_fav == "1") ? 0 : 1
        ]);

        $data = [
            'id' => $product->id,
            'name' => $product->product_name,
            'sku' => $product->product_SKU,
            'product_fav' => ($product->product_fav == "1") ? "1" : "0"
        ];

        return $this->sendResponseV3(true, "Success", $data);
    }

    public function updateInventory(Request $request){
        $validator = Validator::make($request->all(), [
            'id' => 'required|string',
            'type' => 'required|string|in:in,out,setready',
            'quantity' => 'required|decimal:0,3' // amount to add or deduct from stock
        ]);



        if($validator->fails()){
            return $this->sendResponseV3(false, "0", $validator->errors()->first(),400);
        }

        $user = getUser();
        if(!$user){
            return $this->sendResponseV3(false, "0", "Unauthenticated", [], 401);
        }

        $product = Product::where([
            'id' => $request->id,
            'parent_company' => $user['company']['id']
        ])->first();
        if(!$product){
            return $this->sendResponseV3(false, "0", "Product Not Found", [], 404);
        }

        if (isBizappUserCheck()) {
            try {
                // Call the updateInventoryToBizapp function
                $this->updateInventoryToBizapp(auth()->user(), $request->quantity, $product->product_stock, $product, $request->type, $request->note);
                
                // Log success message
                Log::info("updateInventoryToBizapp completed successfully for product: {$product->id}");
            } catch (\Exception $e) {
                // Log the error and return a failure response
                Log::error("updateInventoryToBizapp failed for product: {$product->id}. Error: " . $e->getMessage());
                return $this->sendResponseV3(false, "0", "Failed to update inventory in Bizapp", [], 500);
            }
        }

        try {
        if($request->type == "in"){
            if($product->product_stock == -100){
                $product->update([
                    'product_stock' => $request->quantity,
                    'product_stock_status' => "N"
                ]);
            } else {
                $product->increment('product_stock', $request->quantity);
                // Fetch the updated stock value after incrementing
                $product->refresh();
            }
            logStockChange(
                $user['company']['id'],
                $user['id'],
                $product->id,
                $user['username'] ?? 'update from bizapp web',
                $product->product_name,
                $product->product_SKU ?? "",
                $request->quantity,
                $product->product_stock,
                $request->note ?? 'Add stock in'
            );
        } else if($request->type == "out"){
            if($product->product_stock < $request->quantity){
                return $this->sendResponseV3(false,'0',"Insufficient stock",[],400);
            } else {
                $product->decrement('product_stock', $request->quantity);
                $product->refresh();

                logStockChange(
                    $user['company']['id'],
                    $user['id'],
                    $product->id,
                    $user['username'] ?? 'update from bizapppos app',
                    $product->product_name,
                    $product->product_SKU ?? "",
                    $request->quantity,
                    $product->product_stock,
                    $request->note ?? 'Deduct stock in'
                );
            }
        } else if($request->type == "setready"){
            $product->update([
                'product_stock_status' => "Y",
                'product_stock' => "-100"
            ]);

            logStockChange(
                $user['company']['id'],
                $user['id'],
                $product->id,
                $user['username'] ?? 'update from bizappos app',
                $product->product_name,
                $product->product_SKU ?? "",
                $request->quantity,
                $product->product_stock,
                $request->note ?? 'Set to ready stock'
            );
        }

        $data = [
            'id' => $product->id,
            'name' => $product->product_name,
            'sku' => $product->product_SKU,
            'stock' => $product->product_stock
        ];

        // $title = "Inventory";
        // $description = "[ {$request->type} ] Product [ {$product->product_name } ] Qty [ {$request->quantity} ]";
        // addStaffActivity($title, $description);

        return $this->sendResponseV3(true, "1", "Inventory update success", $data,200);
     } catch (\Exception $e) {
        return $this->sendResponseV3(false, "0", "Inventory update failed", $e->getMessage(), 400);
     }

    }

    public function getCollections(Request $request)
    {
        $user = auth()->user();
        $company_id = auth()->user()->companies->id ?? auth()->user()->employee->parent_company;

        $baseQuery = Collection::where('company_id', $company_id)
            ->where('collection_status', 'active')
            ->withCount(['collectionProducts' => function($query) {
                $query->whereNull('deleted_at')
                ->whereHas('products', function($q) {
                    $q->whereNull('deleted_at');
                });
            }]);


        // Maintain old order behavior
        if(isBizappUserCheck()) {
            $baseQuery->orderBy('koleksiid_bizapp', 'DESC');
        } else {
            $baseQuery->orderBy('collection_name', 'ASC');
        }

        // Check for pagination request
        if($request->has('paginate')) {
            $collections = $baseQuery->paginate($request->input('per_page', 10));

            $responseData = [
                'list' => $collections->items(),
                'pagination' => [
                    'total' => $collections->total(),
                    'per_page' => $collections->perPage(),
                    'current_page' => $collections->currentPage(),
                    'last_page' => $collections->lastPage(),
                    'from' => $collections->firstItem(),
                    'to' => $collections->lastItem()
                ]
            ];

        } else {
            // Original non-breaking format
            $collections = $baseQuery->get();
            $responseData = $collections;
        }
        if($collections->isNotEmpty()) {
            // Format response with product counts
            $formattedCollections = $collections->map(function($collection) {
                return [
                    'id' => $collection->id,
                    'name' => $collection->collection_name,
                    'product_count' => $collection->products_count,
                    'created_at' => $collection->created_at,
                    'updated_at' => $collection->updated_at,
                ];
            });

            return $this->sendResponseV3(
                true,
                "1",
                "Collections retrieved",
                $responseData,
                200
            );
        }

        // Existing sync fallback remains unchanged
        // if(isBizappUserCheck()) {
        //     $urlPos = config('bizappos.bizappos_api_url');
        //     $getAllCollectionsAPI = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_LIST_KOLEKSI', [
        //         'DOMAIN' => $user->domain,
        //         'pid' => $user->pid,
        //         'TOKEN' => 'aa'
        //     ])->throw()->json();

        //     if($getAllCollectionsAPI !== false) {
        //         SyncProductCollectionJob::dispatch($user)->onQueue('syncProductCollection');
        //     }
        // }

        return $this->sendResponseV3(false, "0", "No collections found", [], 404);
    }

    public function getProductCollections(Request $request){

        $validator = Validator::make($request->all(), [
            'col_id' => 'required|string',
        ]);

        if($validator->fails()){
            return $this->sendResponseV3(false, "0", $validator->errors()->first(),400);
        }

        $user = getUser();
        //TOOD : fill the collection_id column in products table to simplify this query
        // $products = Product::with('productDetail')
        // ->join('collection_products', 'products.id', '=', 'collection_products.product_id')
        // ->join('collections', 'collections.id', '=', 'collection_products.collection_id')
        // ->where('collections.company_id', $user['company']['id'])
        // ->where('collections.id', $request->col_id)
        // ->where('collections.collection_status', 'active')
        // ->join('product_details', 'products.id', '=', 'product_details.product_id')
        // ->select('products.*', 'product_details.product_attachment')
        // ->paginate(100);

        $products = Product::with('productDetail')
        ->join('collection_products', 'products.id', '=', 'collection_products.product_id')
        ->join('collections', 'collections.id', '=', 'collection_products.collection_id')
        ->join('product_details', 'products.id', '=', 'product_details.product_id')
        ->where('collections.company_id', $user['company']['id'])
        ->where('collections.id', $request->col_id)
        ->where('collections.collection_status', 'active')
        ->where('products.parent_company', $user['company']['id'])
        ->select('products.*', 'product_details.product_attachment')
        ->paginate(100);

        if($products){
            return $this->sendResponseV3(true, "1", "Product from collections retrieved",$products,200);
        } else {
            return $this->sendResponseV3(false, "0","No products collection", [], 400);
        }
    }

    public function syncProduct(Request $request){
        $user = $request->user();
        if($user->isBizappUser !== "Y"){
            return response()->json([
                'result' => false,
                'status' => '0',
                'message' => "You're not bizapp user"
            ]);
        }

        SyncProductBizappJob::dispatch($user)->onQueue('syncProduct');

        return response()->json([
            'result' => true,
            'status' => '1',
            'message' => "Sync will be in proccess , please wait a few minutes"
        ]);
    }

    public function checkSKU(Request $request)
    {

            $company_id = '';
            $company = auth()->user()->companies;
            if(!$company){
                $company_id = auth()->user()->employee->id;
            } else {
                $company_id = $company->id;
            }
            $product = Product::where('product_SKU',$request->sku)->where('parent_company',$company_id)->first();

            if($product){
                return $this->sendResponseV3(false, "0", "SKU already used for product [ {$product->product_name} ] ");

            } else {
                return $this->sendResponseV3(true, "1", "Success, SKU is available");
            }

    }

    /**
     * Audit collection products for company ownership mismatches
     *
     * @param string|null $companyId Specific company to check, null for all companies
     * @return array Array of mismatched products with collection and company details
     */
    public function auditCollectionProductOwnership(?string $companyId = null)
    {
        $query = Collection::query()
            ->select([
                'collections.id as collection_id',
                'collections.collection_name',
                'collections.company_id as collection_company_id',
                'products.id as product_id',
                'products.product_name',
                'products.parent_company as product_company_id',
                'collection_products.id as collection_product_id',
                'companies.com_name as company_name' // Added company name for better reporting
            ])
            ->join('collection_products', 'collections.id', '=', 'collection_products.collection_id')
            ->join('products', 'collection_products.product_id', '=', 'products.id')
            ->join('companies', 'collections.company_id', '=', 'companies.id')
            ->whereColumn('collections.company_id', '!=', 'products.parent_company');

        // If company ID is provided, filter for that specific company
        if ($companyId) {
            $query->where('collections.company_id', $companyId);
        }

        $mismatches = $query->get();

        if ($mismatches->isEmpty()) {
            return [
                'status' => 'success',
                'message' => 'No mismatched products found in collections',
                'mismatches' => [],
                'summary' => []
            ];
        }

        // Group mismatches by company for better overview
        $mismatchesByCompany = $mismatches->groupBy('collection_company_id');

        // Format results for better readability
        $formattedMismatches = $mismatches->map(function ($mismatch) {
            return [
                'collection' => [
                    'id' => $mismatch->collection_id,
                    'name' => $mismatch->collection_name,
                    'company_id' => $mismatch->collection_company_id,
                    'company_name' => $mismatch->company_name
                ],
                'product' => [
                    'id' => $mismatch->product_id,
                    'name' => $mismatch->product_name,
                    'company_id' => $mismatch->product_company_id
                ],
                'collection_product_id' => $mismatch->collection_product_id
            ];
        });

        // Create summary statistics
        $summary = $mismatchesByCompany->map(function ($companyMismatches, $companyId) {
            return [
                'company_id' => $companyId,
                'company_name' => $companyMismatches->first()->company_name,
                'total_mismatches' => $companyMismatches->count(),
                'affected_collections' => $companyMismatches->unique('collection_id')->count(),
                'affected_products' => $companyMismatches->unique('product_id')->count()
            ];
        })->values();

        return [
            'status' => 'warning',
            'message' => sprintf(
                'Found %d mismatched products across %d companies',
                $mismatches->count(),
                $mismatchesByCompany->count()
            ),
            'summary' => $summary,
            'mismatches' => $formattedMismatches
        ];
    }

    /**
     * Fix mismatched collection products by removing them
     *
     * @param string $companyId Company ID to fix mismatches for
     * @return array Result of the fix operation
     */
    public function fixCollectionProductMismatches(string $companyId)
    {
        try {
            DB::beginTransaction();

            $mismatchedIds = CollectionProduct::select('collection_products.id')
                ->join('collections', 'collections.id', '=', 'collection_products.collection_id')
                ->join('products', 'collection_products.product_id', '=', 'products.id')
                ->where('collections.company_id', $companyId)
                ->whereColumn('collections.company_id', '!=', 'products.parent_company')
                ->pluck('collection_products.id');

            if ($mismatchedIds->isEmpty()) {
                DB::commit();
                return [
                    'status' => 'success',
                    'message' => 'No mismatches found to fix',
                    'removed_count' => 0
                ];
            }

            // Soft delete the mismatched entries
            CollectionProduct::whereIn('id', $mismatchedIds)->delete();

            DB::commit();

            return [
                'status' => 'success',
                'message' => 'Successfully removed ' . count($mismatchedIds) . ' mismatched products from collections',
                'removed_count' => count($mismatchedIds)
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error fixing collection product mismatches: ' . $e->getMessage(), [
                'company_id' => $companyId,
                'error' => $e
            ]);

            return [
                'status' => 'error',
                'message' => 'Failed to fix mismatches: ' . $e->getMessage()
            ];
        }
    }
}
