<?php

namespace App\Http\Controllers\API\v3;

use App\Models\User;
use App\Models\Company;
use App\Models\UserDetail;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\Payment\Gkash;
use App\Models\Payment\Bizappay;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Payment\GkashSetting;
use Illuminate\Support\Facades\Http;
use App\Models\Payment\GkashRegistration;
use App\Http\Controllers\API\BizappayController;

class GkashController extends Controller
{
    public function gkashCallback() 
    {
        $response = request()->all();

            try {
                Log::info("gkash order callback" . json_encode($response));
               
                // You may want to validate the signature before proceeding
                // $receivedSignature = $callbackData['signature'];
                // $computedSignature = hash('sha256', 'YourSecretKey' . $callbackData['amount'] . $callbackData['currency']); // Example signature logic, adjust to Gkash's requirements
                
                
                $saveOrder = null;
                // Process the payment status
                if ($response['status'] === '88 - Transferred') {
                    
                    $saveOrder = new Gkash([
                    'status' => $response['Status'] ?? $response['status'],
                    'payment_method' => $response['paymentMethod'] ?? 5,
                    'PaymentType' => $response['PaymentType'] ?? null,
                    'reference' => $response['Reference'] ?? null,
                    'cartID' => $response['CartID'] ?? $response['cartid'],
                    'CID' => $response['CID'] ?? null,
                    'POID' => $response['PORemID'] ?? null,
                    'description' => $response['description'] ?? null,
                    'pay_amount' => $response['TransferAmount'] ?? $response['amount']
                    ]);     
                    $saveOrder->save();
                    
                    // Return "OK" to acknowledge the callback was received and processed
                    return response('OK', 200);
                    
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Order not transferred',
                        'data' => null
                    ]);
                }

            } catch (\Exception $e) {
                Log::error("error saving gkash order {$e->getMessage()}");
                Log::info("error saving gkash order " . json_encode($response));

                return response()->json([
                    'success' => false,
                    'message' => 'Error saving order: ' . $e->getMessage()
                ], 500);
            }
    }

    public function gkashCheckPaymentStatus(Request $request)
    {
       
        $findRefCode = Gkash::where('reference',$request->refCode)->first();

        if ($findRefCode){
            Log::info("Refcode found : {$findRefCode}");
            return response()->json([
                'success' => true,
                'data' => $findRefCode,
                'message' => 'RefCode is not null'
            ]);
        } else if (!$findRefCode) {
            Log::info("Refcode not found : {$request->refCode}");
            return response()->json([
                'success' => false,
                'data' => $findRefCode,
                'message' => 'refCode not found'
            ]);
        } else {
            Log::info('Refcode is null');
            return response()->json([
                'success' => false,
                'data' => 'refcode is null',
                'message' => 'RefCode is null'
            ]);
        }
    }

    public function gkashRefund()
    {
        $response = request()->all();
        $user = User::where('pid',$response['pid'])->first();
        if(!$user){
            return response()->json([
                'status' => '0',
                'message' => 'User data not found, please contact <NAME_EMAIL>'
            ]);
        }
        // get value from DB
        $findId = Gkash::where('order_id',$response['orderId'])->where('pid',$response['pid'])->first();
        if(!$findId){
            return response()->json([
                'status' => '0',
                'message' => 'Something is wrong, please contact admin. ERRCODE:GC110'
            ]);
        }
        $getSignatureKey = GkashSetting::Select('signature_key')->where('user_id',$user->id)->first();
        if(!$user){
            return response()->json([
                'status' => '0',
                'message' => 'Signature key not found, please update signature key at backoffice.'
            ]);
        }
        
        $amount = str_replace('.', '', $findId->pay_amount);
        // $amount = str_pad($amount, 3, '0', STR_PAD_LEFT);
       
        $signature = "$getSignatureKey;{$response['CID']};{$findId->cartID};{$amount};MYR";
        // dd($signature);
        $hashedSignature = hash("sha512", strtoupper($signature));
        // $url = "https://api-staging.pay.asia/api/payment/refund"; // staging url
        $url = "https://api.gkash.my/api/payment/refund"; // production url

        $getRefund = Http::dump()->post($url, [
            "version" => "1.3.1",
            "CID" => $response['CID'],
            "cartid" => $findId->cartID,
            "currency" => "MYR",
            "amount" => $findId->pay_amount,
            "signature" => $hashedSignature
        ])->throw()->json();

            $findId->status = $getRefund['status'];
            $findId->save();       

        return response()->json([
            $getRefund,
            $signature
        ]);
    }

    public function gkashUpdateReference()
    {
        $response = request()->all();
        Log::info("updating gkash reference : " . json_encode($response));

        $getReference = Gkash::where('reference',$response['refCode'])->first();
        $getReference->pid = $response['pid'];
        $getReference->order_id = $response['newRefCode'];
        $getReference->domain = $response['DOMAIN'];
        $getReference->save();

        Log::info("Refcode updated : {$getReference}" . json_encode($response));
        return response()->json([
            'success' => true,
            'data' => $getReference
        ]);
    }

    public function updateExtrasToStatus()
    {
        $response = request()->all();

        $getStatus = Gkash::where('reference',$response['refCode'])->first();

        $getStatus->status = $response['extras'];
        $getStatus->save();

        return response()->json([
            $getStatus
        ]);
    }



    // ------------------------------------------------   GKASH Registration --------------------------------------------------------

    public function getRegistrationInfo(Request $request)
    {
        $response = request()->all();
        
        // check if the user is in the database
        $pid = $response['pid']; 
        $findPid = User::where('pid',$pid)->first();
        if ($findPid == null){
                // login with bizapp credentials
                $urlPos = config('bizappos.bizappos_api_url');
                $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                    'DOMAIN' => $response['loginDomain'],
                    'username' => $response['loginUsername'],
                    'password' => $response['loginPassword'],
                    'platform' => 'POS'
                ])->throw()->json();
                        
                $user = User::create([
                    'username' => $bizappLogin[0]['penggunaid'],
                    'password' => bcrypt($bizappLogin[0]['katalaluan']),
                    'email' => $bizappLogin[0]['emel'],
                    'isBizappUser' => 'Y',
                    'domain' => $response['loginDomain'],
                    'pid' => $bizappLogin[0]['pid']
                ]);
                $user->save();
    
                $userDetail = UserDetail::create([
                    'user_id' => $user->id,
                    'first_name' => $bizappLogin[0]['nama'],
                    'mobile' => $bizappLogin[0]['nohp'],
                    'avatar' => $bizappLogin[0]['attachmentphoto'],
                    'address' => $bizappLogin[0]['alamat1'] . ', ' . $bizappLogin[0]['alamat2'] . ', ' . $bizappLogin[0]['alamat3'],
                    
                    'state' => $bizappLogin[0]['negeri'],
                    'postcode' => $bizappLogin[0]['poskod'],
                    'country' => $bizappLogin[0]['country']
                ]);
                $userDetail->save();
                
                $company = Company::create([
                    'user_id' => $user->id,
                    'com_name' => $bizappLogin[0]['namaboss'],
                    'com_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
                    'account_type' => 'company',
                    'com_state' => $bizappLogin[0]['negeriboss'],
                    'com_postcode' => $bizappLogin[0]['poskodboss'],
                    'com_mobile' => $bizappLogin[0]['nohpboss'],
                    'com_email' => $bizappLogin[0]['emelboss'],
                    'com_country' => $bizappLogin[0]['country']
                ]);
                $company->save();

                    
                // check if user role is staff
                if ($bizappLogin[0]['roleid'] == "4"){
                    $body = ['DOMAIN' => $request->domain, 'pid' => $bizappLogin[0]['pid'], 'TOKEN' => 'aa'];

                    $bizappAccessModule = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_MODULE_ACCESS_BYPID', $body);

                    
                    $user->access_module = $bizappAccessModule->body();
                    $user->save();
                }
            }
            if(isset($response['document1'])){
                // Retrieve the PDF file from the request
                $ssmCert = $response['document1'];

                // Generate a unique filename
                $filename =  $response['companyName'] . '/' . 'ssm' . '.' . $ssmCert->getClientOriginalExtension();

                // Store the PDF file in the storage directory
                $ssmCert->storeAs('pdfs', $filename, 'public'); // 'public' is the storage disk name
                $document1Path = 'pdfs/' . $filename;
            }
            if(isset($response['document2'])){
                $nricDoc = $response['document2'];

                $filename =  $response['companyName'] . '/' . 'nric' . '.' . $nricDoc->getClientOriginalExtension();

                $nricDoc->storeAs('pdfs', $filename, 'public');
                $document2Path = 'pdfs/' . $filename;
            }
            if(isset($response['document3'])){
                $bankStatement = $response['document3'];

                $filename = $response['companyName'] . '/' . 'bankStmt' . '.' . $bankStatement->getClientOriginalExtension();

                $bankStatement->storeAs('pdfs', $filename, 'public'); 
                $document3Path = 'pdfs/' . $filename;
            }
  
            if(isset($response['document4'])){
                $premisePhoto = $response['document4'];

                $filename = $response['companyName'] . '/' . 'prmsPhoto' . '.' . $premisePhoto->getClientOriginalExtension();

                $premisePhoto->storeAs('pdfs', $filename, 'public'); 
                $document4Path = 'pdfs/' . $filename;
            }
    
            if(isset($response['document5'])) {
                $otherDoc = $response['document5'];

                $filename = $response['companyName'] . '/' . 'otherDoc' . '.' . $otherDoc->getClientOriginalExtension();

                $otherDoc->storeAs('pdfs', $filename, 'public');
                $document5Path = 'pdfs/' . $filename;
            }
            if(isset($response['signatureFile'])) {
                $signatureFile = $response['signatureFile'];

                $signtureFilename = $response['companyName'] . '/' . 'signature' . '.' . $signatureFile->getClientOriginalExtension();
            
                $signatureFile->storeAs('signature', $signtureFilename, 'public');
                $signatureFilePath = 'signature/' . $signtureFilename;
            }

            $findPid2 = User::where('pid',$pid)->first(); // find the user again
            $saveRegistration = GkashRegistration::updateOrCreate(
                [
                    'user_id' => $findPid2->id
                ],
                [
                'companyName' => $response['companyName'],
                'companySSM' => $response['companySSM'],
                'companyDBA' => $response['companyDBA'],
                'companyEmail' => $response['companyEmail'],
                'companyAdd1' => $response['companyAdd1'],
                'companyAdd2' => $response['companyAdd2'] ?? '',
                'companyAdd3' => $response['companyAdd3'] ?? '',
                'companyPostcode' => $response['companyPostcode'],
                'companyCity' => $response['companyCity'],
                'companyState' => $response['companyState'],
                'companyCountry' => $response['companyCountry'],
                'businessType' => $response['businessType'],
                'businessNature' => $response['businessNature'],
                'businessProductPrice' => $response['businessProductPrice'],
                'businessAMS' => $response['businessAMS'],
                'contactPersonName' => $response['contactPersonName'],
                'contactPersonNRIC' => $response['contactPersonNRIC'],
                'contactPersonPosition' => $response['contactPersonPosition'],
                'contactPersonPhone' => $response['contactPersonPhone'],
                'companyBankName' => $response['companyBankName'],
                'companyAccountName' => $response['companyAccountName'],
                'companyAccountNumber' => $response['companyAccountNumber'],
                'pepName' => $response['pepName'] ?? null,
                'pepPosition' => $response['pepPosition'] ?? null,
                'pepRelationship' => $response['pepRelationship'] ?? null,
                'documentSSM' => $document1Path ?? null,
                'documentNRIC' => $document2Path ?? null,
                'documentBankStatement' => $document3Path ?? null,
                'documentPoB' => $document4Path ?? null,
                'documentOthers' => $document5Path ?? null,
                'signatureFile' => $signatureFilePath ?? null,
            ]);
            $saveRegistration->save();

            // BIZAPPAY - GET TOKEN
            $bizappayToken = (new BizappayController)->generateToken();
            
            // BIZAPPAY - GENERATE BILL
            $getApiKey = config('services.bizappay.key');
            $bizappayUrl = config('services.bizappay.url');
    
            $bizappayPage = Http::withHeaders([
                'Authentication' => $bizappayToken,
            ])
            ->asForm()
            ->post($bizappayUrl . 'bill/create', [
                'apiKey' => $getApiKey,
                'category' => 'e2audfn6', //  4z6h3kuf <-- staging code || e2audfn6 <-- production code
                'name' => 'Bizappos PLUS Registration',
                'amount' => '413.40',
                'payer_name' => $response['contactPersonName'],
                'payer_email' => $response['companyEmail'],
                'payer_phone' => $response['contactPersonPhone'],
                'webreturn_url' => route('payment.done'),
                'callback_url' => route('api.payment.done'),
            ])->throw()->json();
        

            if($bizappayPage['status'] == "ok"){
                $updateBillcode = GkashRegistration::where('user_id',$saveRegistration->user_id)->first();
                $updateBillcode->billcode = $bizappayPage['billCode'];
                $updateBillcode->save();

                $updateBizappayTable = new Bizappay([
                    'billcode' => $bizappayPage['billCode'],
                    'categorycode' => 'e2audfn6',  // 4z6h3kuf <-- staging code || e2audfn6 <-- production code
                    'paidamount' => '413.40',
                    'paymentstatus' => 'Pending'
                ]);
                $updateBizappayTable->save();
            }

            if ($saveRegistration->exists) {
                return response()->json([
                    'success' => true,
                    'bizappayUrl' => $bizappayPage['url'], 
                    'data' => $saveRegistration
                ]);
             } else {
                return response()->json([
                    'success' => false
                ]);
             }
       
    }

    public function testGkashPost()
    {
        // BIZAPPAY - GET TOKEN
        $bizappayToken = (new BizappayController)->generateToken();
            
        // BIZAPPAY - GENERATE BILL
        $getApiKey = config('services.bizappay.key');
        $bizappayUrl = config('services.bizappay.url');

        $bizappayPage = Http::withHeaders([
            'Authentication' => $bizappayToken
        ])
        ->asForm()
        ->post($bizappayUrl . 'bill/create', [
            'apiKey' => $getApiKey,
            'category' => '4z6h3kuf',
            'name' => 'Bizappos Payment Gateway',
            'amount' => '379.00',
            'payer_name' => 'contactPersonName',
            'payer_email' => '<EMAIL>',
            'payer_phone' => '01928372637',
            'webreturn_url' => route('payment.done'),
            'callback_url' => route('api.payment.done'),
        ])->throw()->json();
        
        // dd($bizappayPage);
        return redirect($bizappayPage['url']);
    }

    public function bizappayCallback(Request $request)
    {
            //     array:4 [▼
            //     "billcode" => "PXwpVyS4Q3"
            //     "billstatus" => "3"
            //     "billinvoice" => "BM04286272362101090723"
            //     "billtrans" => "2023-07-09 01:21:36"
            //   ]
            $payment_trx = GkashRegistration::where('billcode', $request->billcode)->first();
            $bizappay_table = Bizappay::where('billcode',$request->billcode)->first();

            if($request->billstatus == "1")
            {
                $payment_trx->status = 'paid';
                $payment_trx->save();
                
                $bizappay_table->invoice = $request->billinvoice;
                $bizappay_table->paymentstatus = $request->billstatus;
                $bizappay_table->save();
    
            } else {
                $payment_trx->status = $request->billstatus;
                $payment_trx->save();
            }
            return response()->json([
                'status' => $request->billstatus
            ]);
    }


    public function testWebreturn(Request $request) 
    {

        // billstatus (1-success, 2-pending, 3-failed, 4-no action) - return by bizappay API
        if($request->billstatus == "1")
        {       
            return view('frontend.payment.payment-success');
        }
        else
        {
            return view('frontend.payment.payment-fail');
        }

    }

    public function checkBillCodeStatus(Request $request)
    {
        $paymentStatus = 'Failed'; 

        // get user uuid
        $getUser = User::Where('pid', $request->pid)->first();
        if(!$getUser){
            return response()->json([
                'status' => '0',
                'message' => 'Bill status code : User ID not found, please contact support'
            ]);
        }
        // BIZAPPAY - GET TOKEN
        $bizappayToken = (new BizappayController)->generateToken();
            

        if ($getUser->gkashRegister != null) {
        // BIZAPPAY - GENERATE BILL
        $getApiKey = config('services.bizappay.key');
        $bizappayUrl = config('services.bizappay.url');

        $bizappayStatus = Http::withHeaders([
            'Authentication' => $bizappayToken,
        ])
        ->asForm()
        ->post($bizappayUrl . 'bill/info', [
            'apiKey' => $getApiKey,
            'search_str' => $getUser->gkashRegister->billcode
        ])->throw()->json();

        // save / update status to DB
        $bizappayTable = Bizappay::where('billcode',$bizappayStatus['bill']['billcode'])->first();
        $bizappayTable->paymentstatus = $bizappayStatus['bill']['payments'][0]['status'];
        $bizappayTable->save();

        // dd($bizappayStatus['bill']['payments'][0]['status']); 
        if($bizappayStatus['bill']['payments'][0]['status'] == 1) {
            $paymentStatus = 'Success';
        } else if ($bizappayStatus['bill']['payments'][0]['status'] == 2) {
            $paymentStatus = 'Pending';
        }

        return response()->json([
            'paymentStatus' => $paymentStatus,
            'gkashApproval' => 'Pending'
        ]);
    } else {
        return response()->json([
            'paymentStatus' => '0',
            'gkashApproval' => 'Not submitted'
        ]);
    }

    }
}

