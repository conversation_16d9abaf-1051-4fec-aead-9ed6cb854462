<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\EMandateDataValidationService;
use App\Models\User;
use App\Models\UserDetail;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class EMandateDataController extends Controller
{
    protected $validationService;

    public function __construct(EMandateDataValidationService $validationService)
    {
        $this->validationService = $validationService;
    }

    /**
     * Show the e-mandate data collection form
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function showDataForm(Request $request)
    {
        $user = Auth::user();
        $userDetail = $user->userDetails;
        $company = $user->companies;
        
        // Get validation results
        $validation = $this->validationService->validateUserEMandateData($user);
        $messages = $this->validationService->getValidationMessages($validation);
        
        // Get return URL for after successful data collection
        $returnUrl = $request->get('return_url', route('user.subscriptions.management'));
        $subscriptionId = $request->get('subscription_id');
        
        return view('user.emandate.data-collection', compact(
            'user', 
            'userDetail', 
            'company', 
            'validation', 
            'messages', 
            'returnUrl',
            'subscriptionId'
        ));
    }

    /**
     * Update user e-mandate data
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateData(Request $request)
    {
        $user = Auth::user();
        
        // Validate the request
        $validatedData = $request->validate([
            // User details
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'mobile' => 'required|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postcode' => 'nullable|string|max:10',
            'country' => 'nullable|string|max:100',
            
            // Company details
            'ic_number' => 'required|string|max:20',
            'com_name' => 'required|string|max:255',
            'com_mobile' => 'nullable|string|max:20',
            'com_email' => 'nullable|email|max:255',
            'com_registration_no' => 'nullable|string|max:50',
            'com_address' => 'nullable|string|max:500',
            'com_city' => 'nullable|string|max:100',
            'com_state' => 'nullable|string|max:100',
            'com_postcode' => 'nullable|string|max:10',
            'com_country' => 'nullable|string|max:100',
            
            // Return URL
            'return_url' => 'nullable|url',
            'subscription_id' => 'nullable|string'
        ]);

        try {
            DB::beginTransaction();

            // Update or create user details
            $userDetailData = [
                'first_name' => $validatedData['first_name'],
                'last_name' => $validatedData['last_name'],
                'mobile' => $validatedData['mobile'],
                'address' => $validatedData['address'] ?? null,
                'city' => $validatedData['city'] ?? null,
                'state' => $validatedData['state'] ?? null,
                'postcode' => $validatedData['postcode'] ?? null,
                'country' => $validatedData['country'] ?? 'Malaysia',
            ];

            if ($user->userDetails) {
                $user->userDetails->update($userDetailData);
            } else {
                $userDetailData['user_id'] = $user->id;
                UserDetail::create($userDetailData);
            }

            // Update company details
            $companyData = [
                'ic_number' => $validatedData['ic_number'],
                'com_name' => $validatedData['com_name'],
                'com_mobile' => $validatedData['com_mobile'] ?? null,
                'com_email' => $validatedData['com_email'] ?? null,
                'com_registration_no' => $validatedData['com_registration_no'] ?? null,
                'com_address' => $validatedData['com_address'] ?? null,
                'com_city' => $validatedData['com_city'] ?? null,
                'com_state' => $validatedData['com_state'] ?? null,
                'com_postcode' => $validatedData['com_postcode'] ?? null,
                'com_country' => $validatedData['com_country'] ?? 'Malaysia',
            ];

            if ($user->companies) {
                $user->companies->update($companyData);
            } else {
                $companyData['user_id'] = $user->id;
                Company::create($companyData);
            }

            DB::commit();

            // Validate the updated data
            $validation = $this->validationService->validateUserEMandateData($user->fresh());
            
            if ($validation['is_complete']) {
                // Data is now complete, redirect to appropriate next step
                $returnUrl = $validatedData['return_url'] ?? route('user.subscriptions.management');
                $subscriptionId = $validatedData['subscription_id'] ?? null;
                
                if ($subscriptionId) {
                    // Redirect back to subscription payment with updated data
                    return redirect()->route('user.subscriptions.payment', $subscriptionId)
                        ->with('success', 'Your profile has been updated successfully. You can now proceed with Direct Debit setup.');
                } else {
                    // Redirect to the specified return URL
                    return redirect($returnUrl)
                        ->with('success', 'Your e-mandate profile data has been updated successfully.');
                }
            } else {
                // Still missing some data, show validation messages
                $messages = $this->validationService->getValidationMessages($validation);
                return redirect()->back()
                    ->with('warning', 'Some required information is still missing.')
                    ->with('validation_messages', $messages)
                    ->withInput();
            }

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Error updating e-mandate data', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withError('An error occurred while updating your information. Please try again.')
                ->withInput();
        }
    }

    /**
     * Check user data completeness (AJAX endpoint)
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkDataCompleteness()
    {
        $user = Auth::user();
        $result = $this->validationService->canProceedWithEMandate($user);
        
        return response()->json($result);
    }

    /**
     * Validate specific field (AJAX endpoint)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateField(Request $request)
    {
        $field = $request->get('field');
        $value = $request->get('value');
        
        $isValid = false;
        $message = '';
        
        switch ($field) {
            case 'mobile':
            case 'com_mobile':
                $isValid = $this->validationService->isValidMalaysianPhoneNumber($value);
                $message = $isValid ? 'Valid phone number' : 'Please enter a valid Malaysian phone number';
                break;
                
            case 'ic_number':
                $isValid = $this->validationService->isValidMalaysianIC($value);
                $message = $isValid ? 'Valid IC number' : 'Please enter a valid Malaysian IC number (12 digits)';
                break;
                
            case 'email':
            case 'com_email':
                $isValid = filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
                $message = $isValid ? 'Valid email address' : 'Please enter a valid email address';
                break;
                
            default:
                $isValid = !empty($value);
                $message = $isValid ? 'Field is not empty' : 'This field is required';
        }
        
        return response()->json([
            'valid' => $isValid,
            'message' => $message
        ]);
    }
}
