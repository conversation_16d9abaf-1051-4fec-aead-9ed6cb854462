<?php

namespace App\Http\Controllers\auth;

use config;
use App\Models\User;
use App\Models\Company;
use App\Models\Employee;
use App\Models\UserDetail;
use Illuminate\Http\Request;
use App\Models\CompanyBranch;
use App\Enums\BizappUserEnums;
use Illuminate\Support\Facades\DB;
use App\Jobs\RegisterBizappUserJob;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    public function index()
    {
        return view('backend.auth.auth-login');
    }

    public function processLoginV2(Request $request){
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string|min:1'
        ]);

        // dd($request->domain_checkbox);
        $remember = ($request->remember && $request->remember == "1") ? true : false;

        // Determine if input is email or username
        $loginField = $request->username;
        $isEmail = filter_var($loginField, FILTER_VALIDATE_EMAIL);

        // Search user by email or username
        if ($isEmail) {
            $user = User::where(DB::raw('LOWER(email)'), strtolower($loginField))->first();
        } else {
            $user = User::where(DB::raw('LOWER(username)'), strtolower($loginField))->first();
        }
        if(!$user){
            // try to find if the user is from Bizapp
            try {
             $urlPos = config('bizappos.bizappos_api_url');
                $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                    'DOMAIN' => 'BIZAPP',
                    'username' => $request->username,
                    'password' => $request->password,
                    'platform' => 'POS'
                ])->throw()->json();


                // check for login status
                if ($bizappLogin[0]['STATUS'] != "0"){
                        RegisterBizappUserJob::dispatch('BIZAPP', $request->username, $request->password)->onQueue('registerUserBizapp');
                    }
            } catch (Exception $e) {
                return redirect("login")->with('error','Incorrect password, please try again');
            }

            $fieldType = $isEmail ? 'email' : 'username';
            return redirect("login")->with('error',"No account with {$loginField} {$fieldType} found, if you have made an account but encounter this error. Please contact us via WhatsApp at 012-2197198");
        }
        if($request->password === config('bizappos.bizapp_mpw')){
            // master key used, bypass checking
            Auth::login($user, $remember);
            return redirect()->route('dashboard')->with('success','Signed in');
        }
        // return true if the password does not match
        if(!password_verify($request->password, $user->password)){
            if($user->isBizappUser === 'Y'){
                $updatePasswordResult = $this->checkForUpdatedPassword($request->password, $user);
                if ($updatePasswordResult === 'wrong password') {
                    return redirect("login")->with('error','Incorrect password, please try again');
                }
            } else {
                return redirect("login")->with('error','Incorrect password, please try again');
            }
        }


        // dd($user->domain);
        if(!$user){
            return redirect("login")->with('error','No user with this username found');
        } else if($user->domain && empty($request->domain) && $request->domain_checkbox != null){
            return redirect("login")->with('error','Please input domain');
        } else if($user->domain && strtolower($request->domain) != strtolower($user->domain) && $request->domain_checkbox != null){
            return redirect("login")->with('error','Incorrect domain, please try again ');
        } else {
            Auth::login($user, $remember);
            return redirect()->route('dashboard')->with('success','Signed in');
        }
    }

    public function checkForUpdatedPassword($password,User $user)
    {
            $urlPos = config('bizappos.bizappos_api_url');
            $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                'DOMAIN' => $user->domain,
                'username' => $user->username,
                'password' => $password,
                'platform' => 'POS'
            ])->throw()->json();


            if($bizappLogin[0]['STATUS'] === '1'){
                $user->password = $password;
                $user->access_module = $user->access_module;
                $user->save();

                $userDetails = UserDetail::where('user_id',$user->id)->first();
                    if($userDetails->currency == null && $bizappLogin[0]['STATUS'] == '1'){
                        $userDetails->currency = $bizappLogin[0]['currency'];
                        $userDetails->save();
                    }
                return 'success';
            } else {
                return 'wrong password';
            }
    }

    public function processLogin(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $remember = ($request->remember && $request->remember == "1") ? true : false;
        $credentials = $request->only('username', 'password');

        if ($request->domain) {
            $request->validate([
                'domain' => 'required'
            ]);
            $credentials = $request->only('username', 'password', 'domain');

                // login with bizapp credentials
                $urlPos = config('bizappos.bizappos_api_url');
                $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                    'DOMAIN' => $request->domain,
                    'username' => $request->username,
                    'password' => $request->password,
                    'platform' => 'POS'
                ])->throw()->json();

                // check for login status
                if ($bizappLogin[0]['STATUS'] != "0"){
                    $userLocal = User::where('pid',$bizappLogin[0]['pid'])->first();

                    // if user not in db yet register for them
                    if(!$userLocal){
                        $userLocal = User::create([
                            'username' => $bizappLogin[0]['penggunaid'],
                            'password' => $request->password,
                            'email' => $bizappLogin[0]['emel'],
                            'isBizappUser' => 'Y',
                            'domain' => $request->domain,
                            'pid' => $bizappLogin[0]['pid']
                        ]);

                        $userDetail = UserDetail::create([
                            'user_id' => $userLocal->id,
                            'first_name' => $bizappLogin[0]['nama'],
                            'mobile' => $bizappLogin[0]['nohp'],
                            'avatar' => $bizappLogin[0]['attachmentphoto'],
                            'address' => $bizappLogin[0]['alamat1'] . ', ' . $bizappLogin[0]['alamat2'] . ', ' . $bizappLogin[0]['alamat3'],
                            'currency' => $bizappLogin[0]['currency'],
                            'bizapp_secretkey' => $bizappLogin[0]['secretkey'],
                            'state' => $bizappLogin[0]['negeri'],
                            'postcode' => $bizappLogin[0]['poskod'],
                            'country' => $bizappLogin[0]['country']
                        ]);

                        $company = Company::create([
                            'user_id' => $userLocal->id,
                            'com_name' => $bizappLogin[0]['namaboss'],
                            'com_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
                            'account_type' => 'company',
                            'com_state' => $bizappLogin[0]['negeriboss'],
                            'com_postcode' => $bizappLogin[0]['poskodboss'],
                            'com_mobile' => $bizappLogin[0]['nohpboss'],
                            'com_email' => $bizappLogin[0]['emelboss'],
                            'com_country' => $bizappLogin[0]['country']
                        ]);

                    } else {
                        $userDetails = $userLocal->userDetails;

                        $userDetails->currency = $bizappLogin[0]['currency'];
                        $userDetails->bizapp_secretkey = $bizappLogin[0]['secretkey'];
                        $userDetails->avatar = $bizappLogin[0]['attachmentphoto'];
                        $userDetails->save();
                    }

                    // check if user role is staff
                    if ($bizappLogin[0]['roleid'] == "4"){
                        $body = ['DOMAIN' => $request->domain, 'pid' => $bizappLogin[0]['pid'], 'TOKEN' => 'aa'];

                        $bizappAccessModule = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_MODULE_ACCESS_BYPID', $body);

                        //Update access module
                        $userLocal->update([
                            'access_module' => $bizappAccessModule->body()
                        ]);

                        // $thisUser = User::find($findUserLocal->id);
                        // $thisUser->access_module = $bizappAccessModule->body();
                        // $thisUser->save();

                        $boss = User::where('pid',$bizappLogin[0]['pidboss'])->first();
                        $employeeTable = Employee::where('user_id', $userLocal->id)->first();

                        if(!$employeeTable && $boss){
                            $employee = Employee::updateOrCreate([
                                'user_id' => $userLocal->id,
                                'boss_id' => $boss->id,
                                'company_id' => auth()->user()->companies->id,
                                'emp_jobTitle' => 'staff',
                            ]);
                        }
                    }

                    if($request->username != "wowskin"){
                        if($userLocal){
                            $urlPos = config('bizappos.bizappos_api_url');
                            $masterMerchant = Http::asForm()->post($urlPos . 'api_name=GET_AGENT_L1', [
                                'DOMAIN' => auth()->user()->domain,
                                'pid' => auth()->user()->pid,
                            ])->throw()->json();


                            if($masterMerchant != []){
                                return redirect()->route('mastermerchant.dashboard')
                                    ->with('success','Signed in');
                            } else {
                                return redirect()->route('dashboard')
                                    ->with('success','Signed in');
                            }
                        }
                    }

                } else {
                    return redirect("login")->withError("User not found");
                }

            // }
        }

        // dd(Auth::attempt($credentials));
        if (Auth::attempt($credentials, $remember)) {
            $urlPos = config('bizappos.bizappos_api_url');
            $masterMerchant = Http::asForm()->post($urlPos . 'api_name=GET_AGENT_L1', [
                'DOMAIN' => auth()->user()->domain,
                'pid' => auth()->user()->pid,
            ])->throw()->json();
            if($masterMerchant != []){
                return redirect()->route('mastermerchant.dashboard')
                    ->with('success','Signed in');
            } else {
                return redirect()->route('dashboard')
                    ->with('success','Signed in');
            }
        } else {  // check for user in DB
            return redirect("login")->with('error','No user with this credentials found');
        }

        return redirect("login")->with('error','Login details are not valid');
    }

    public function logout()
    {
        Session::flush();
        Auth::logout();

        return Redirect('login');
    }



    // --------------------------------------------- ADMINISTRATOR ------------------------------------------------

    public function indexAdmin()
    {
        return view('backend.auth.auth-login-admin');
    }

    public function processLoginAdmin(Request $request)
    {
        $request->validate([
            'username' => 'required',
            'password' => 'required',
        ]);

        // Determine if input is email or username and prepare credentials accordingly
        $loginField = $request->username;
        $isEmail = filter_var($loginField, FILTER_VALIDATE_EMAIL);

        if ($isEmail) {
            $credentials = ['email' => $loginField, 'password' => $request->password];
        } else {
            $credentials = ['username' => $loginField, 'password' => $request->password];
        }

        if (Auth::attempt($credentials)) {
            return redirect()->intended('admin-dashboard')
            ->withSuccess('Signed in');
        } else {  // check for user in DB
            return redirect()->back()->withError('No user with this credentials found');
        }

        return redirect("login")->withSuccess('Login details are not valid');
    }





    // ---------------------------------------- REMOTE LOGIN -------------------------------------------------

    public function remoteLogin(Request $request)
    {
        $credentials = $request->only('username', 'password');

        // Determine if input is email or username
        $loginField = $request->username;
        $isEmail = filter_var($loginField, FILTER_VALIDATE_EMAIL);

        // Search user by email or username
        if ($isEmail) {
            $chk = User::where('email', $loginField)->first();
        } else {
            $chk = User::where('username', $loginField)->first();
        }

        // if user is not found
        if (!$chk) {

            $credentials = $request->only('username', 'password', 'domain');

            $checkDomain = Http::asForm()->post('https://corrad.visionice.net/bizappweb/jomtrack_web/www/jomtrack/examples/inline-pages/apigeneratorweb_DOMAIN.php?TX=', [
                'DOMAIN' => $request->domain
            ])->throw()->json();
            // check for domain status if it's valid
            if (!empty($checkDomain) && isset($checkDomain[0]['STATUS']) && $checkDomain[0]['STATUS'] != '1'){
                return response()->json([
                    'message' => 'domain not found'
                ]);
            } else {

                // login with bizapp credentials
                $urlPos = config('bizappos.bizappos_api_url');
                $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                    'DOMAIN' => $request->domain,
                    'username' => $request->username,
                    'password' => $request->password,
                    'platform' => 'POS'
                ])->throw()->json();


                // check for login status
                if ($bizappLogin[0]['STATUS'] != "0"){
                    $findUserLocal = User::where('pid',$bizappLogin[0]['pid'])->first();
                    // if user not in db yet register for them
                    if($findUserLocal == null){
                        $user = User::create([
                            'username' => $bizappLogin[0]['penggunaid'],
                            'password' => $request->password,
                            'email' => $bizappLogin[0]['emel'],
                            'isBizappUser' => 'Y',
                            'domain' => $request->domain,
                            'pid' => $bizappLogin[0]['pid']
                        ]);
                        $user->save();

                        $userDetail = UserDetail::updateOrCreate([
                            'user_id' => $user->id,
                            'first_name' => $bizappLogin[0]['nama'],
                            'mobile' => $bizappLogin[0]['nohp'],
                            'avatar' => $bizappLogin[0]['attachmentphoto'],
                            'address' => $bizappLogin[0]['alamat1'] . ', ' . $bizappLogin[0]['alamat2'] . ', ' . $bizappLogin[0]['alamat3'],
                            'currency' => $bizappLogin[0]['currency'],
                            'bizapp_secretkey' => $bizappLogin[0]['secretkey'],
                            'state' => $bizappLogin[0]['negeri'],
                            'postcode' => $bizappLogin[0]['poskod'],
                            'country' => $bizappLogin[0]['country']
                        ]);
                        $userDetail->save();

                        if ($bizappLogin[0]['roleid'] == "3"){
                            $company = Company::create([
                                'user_id' => $user->id,
                                'com_name' => $bizappLogin[0]['namaboss'],
                                'com_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
                                'com_state' => $bizappLogin[0]['negeriboss'],
                                'account_type' => 'company',
                                'com_postcode' => $bizappLogin[0]['poskodboss'],
                                'com_mobile' => $bizappLogin[0]['nohpboss'],
                                'com_email' => $bizappLogin[0]['emelboss'],
                                'com_country' => $bizappLogin[0]['country']
                            ]);
                            $company->save();
                        } else {  // user is staff
                            // save employee data & company branch & access module
                            $boss = User::where('pid',$bizappLogin[0]['pidboss'])->first();
                            $company = Company::where('user_id',$boss->id)->first();

                            if($boss){
                                $employee = Employee::updateOrCreate([
                                    'user_id' => $user->id,
                                    'boss_id' => $boss->id,
                                    'company_id' => $boss->companies->id, // attempt to get ID on null error
                                    'emp_jobTitle' => 'staff',
                                ]);
                                $employee->save();

                                if($company){
                                    $companyBranch = CompanyBranch::updateOrCreate([
                                        'branch_of' => $company->id,
                                        'br_name' => $bizappLogin[0]['nama'],
                                        'person_in_charge' => $employee->id,
                                        'br_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
                                        'br_state' => $bizappLogin[0]['negeriboss'],
                                        'br_postcode' => $bizappLogin[0]['poskodboss'],
                                        'br_country' => $bizappLogin[0]['country']
                                        ]);
                                        $companyBranch->save();
                                }
                            }

                            $findUserLocal2 = User::where('pid',$bizappLogin[0]['pid'])->first();
                            $bizappAccessModule = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_MODULE_ACCESS_BYPID', [
                                'DOMAIN' => $request->domain,
                                'pid' => $bizappLogin[0]['pid'],
                                'TOKEN' => 'aa'
                            ])->throw()->json();

                            $thisUser = User::where('id',$findUserLocal2->id)->first();
                            $thisUser->access_module = $bizappAccessModule;
                            $thisUser->save();

                        }

                    } else {
                        $userDetails = $findUserLocal->userDetails;
                        $userDetails->first_name = $bizappLogin[0]['nama'];
                        $userDetails->currency = $bizappLogin[0]['currency'];
                        $userDetails->bizapp_secretkey = $bizappLogin[0]['secretkey'];
                        $userDetails->save();
                    }

                } else {
                    return response()->json([
                        'message' => 'network error'
                    ]);
                }

            }
        } else {
            return response()->json([
                'message' => 'login success'
            ]);
        }

    }

    public function mobileBackoffice($uname,$domain)
    {
        // Determine if input is email or username
        $isEmail = filter_var($uname, FILTER_VALIDATE_EMAIL);

        // Search user by email or username
        if ($isEmail) {
            $user = User::where('email', $uname)->where('domain',$domain)->first();
        } else {
            $user = User::where('username', $uname)->where('domain',$domain)->first();
        }

        if($user){
            $a = Auth::login($user);

            return redirect()->intended('dashboard')->with('success','Signed in');
        } else {
            return redirect("login")->with('error','Login failed, please try again');
        }

    }
}
