<?php

namespace App\Http\Controllers\auth;

use Exception;
use App\Models\User;
use App\Models\Company;
use App\Models\Receipt;
use App\Models\Category;
use App\Models\UserDetail;
use Illuminate\Http\Request;
use App\Services\BayarCashService;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use App\Models\Payment\Bizappay;
use App\Models\Payment\BayarCash;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Http\Controllers\API\BizappayController;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class RegisterController extends Controller
{
    /**
     * Show the prime trial registration form
     */
    public function primeTrial()
    {
        return view('auth.register-prime-trial');
    }

    /**
     * Handle prime trial registration
     */
    public function processPrimeTrial(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email|unique:users,email',
            'business_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20'
        ]);

        // Create user with trial account
        $password = Str::random(12);
        $user = User::create([
            'email' => $validated['email'],
            'name' => $validated['business_name'],
            'phone' => $validated['phone'],
            'password' => Hash::make($password),
            'trial_ends_at' => '2025-06-14 23:59:59'
        ]);

        // TODO: Send email notification with credentials
        // $user->notify(new TrialAccountCreated($password));

        return redirect()->back()->with('success', 'Trial account created! Check your email for login details.');
    }

    /**
     * @var BayarCashService
     */
    protected $bayarCashService;

    /**
     * Constructor
     */
    public function __construct(BayarCashService $bayarCashService)
    {
        $this->bayarCashService = $bayarCashService;
    }

    public function checkUsername(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|unique:users,username'
        ]);

        return response()->json([
            'available' => !$validator->fails(),
            'message' => $validator->fails() ? 'Username cannot be used' : 'Username available'
        ]);
    }

    public function index()
    {
        $categories = Category::whereNull('parent_id')->orderBy('name', 'ASC')->get();

        // Fetch active subscription plans
        $subscriptionPlans = SubscriptionPlan::where('is_active', true)
                                ->orderBy('price')
                                ->get();

        return view('backend.auth.auth-register', compact('categories', 'subscriptionPlans'));
    }

    public function processRegister(Request $request)
    {
        $this->validate(request(), [
            'email' => 'required|email|unique:users,email',
            'username' => 'required|unique:users,username',
            'mobile' => 'required|min:10|max:11',
            'password' => 'required|min:4',
            'cPassword' => 'required|same:password',
            'subscription_plan' => 'required|exists:subscription_plans,id',
            'payment_method' => 'required|in:bizappay,bayarcash'
        ]);

        // Get the selected subscription plan
        $subscriptionPlan = SubscriptionPlan::findOrFail($request->subscription_plan);

        // If the plan has a price and no trial period, and the payment method is BayarCash,
        // we need to validate the payment gateway before creating the user
        if ($subscriptionPlan->price > 0 && !$subscriptionPlan->trial_period_days && $request->payment_method === 'bayarcash') {
            // Validate BayarCash payment gateway before creating the user
            $validationResult = $this->validateBayarCashGateway();

            if (!$validationResult['success']) {
                return redirect()->back()
                    ->withInput()
                    ->withError($validationResult['message']);
            }
        }

        // Create the user
        $user = User::create(request(['username', 'email', 'password']));

        try {
            $userDetail = UserDetail::create([
                'user_id' => $user->id,
                'mobile' => $request->mobile
            ]);
            $userDetail->save();

            $company = Company::create([
                'user_id' => $user->id,
                'com_name' => $request->username,
                'account_type' => 'company',
                'com_sst_value' => '0'
            ]);
            $company->save();

            $receipt = Receipt::create([
                'company_id' => $company->id,
                'user_id' => $user->id,
                'title' => '',
                'email' => $request->email,
                'phone' => $request->mobile,
                'sst' => '0,0',
            ]);

            // Calculate subscription dates
            $startsAt = now();
            $endsAt = null;

            if ($subscriptionPlan->duration_in_seconds > 0) {
                $endsAt = $startsAt->copy()->addSeconds($subscriptionPlan->duration_in_seconds);
            }

            // Calculate trial period if applicable
            $trialEndsAt = null;
            if ($subscriptionPlan->trial_period_days > 0) {
                $trialEndsAt = $startsAt->copy()->addDays($subscriptionPlan->trial_period_days);
                // If there's a trial, the subscription starts after the trial
                $endsAt = $trialEndsAt->copy()->addSeconds($subscriptionPlan->duration_in_seconds);
            }

            // Create new subscription
            $subscription = Subscription::create([
                'company_id' => $company->id,
                'subscription_plan_id' => $subscriptionPlan->id,
                'starts_at' => $startsAt,
                'ends_at' => $endsAt,
                'trial_ends_at' => $trialEndsAt,
                'status' => 'active'
            ]);


            // Log the user in immediately after registration
            auth()->login($user);

            // Process payment if plan has a price and no trial period
            if ($subscriptionPlan->price > 0 && !$subscriptionPlan->trial_period_days) {
                // Format amount to 2 decimal places
                $amount = number_format($subscriptionPlan->price, 2, '.', '');

                // Process payment based on selected payment method
                if ($request->payment_method === 'bayarcash') {
                    // Process BayarCash payment
                    return $this->processBayarCashPayment($subscription, $user, $company, $amount);
                } else {
                    // Process Bizappay payment (existing flow)
                    return $this->processBizappayPayment($subscription, $user, $amount);
                }
            }

            // return redirect()->route('dashboard')->withSuccess("Account successfully registered.");

        } catch (Exception $e) {
            // If there's an error, delete the user and related records properly
            if (isset($user) && $user) {
                try {
                    // Use our new method to properly delete the user and all related records
                    $user->deleteWithRelations();
                } catch (Exception $deleteException) {
                    // Log the deletion error but continue with the original error
                    Log::error('Error deleting user during registration rollback: ' . $deleteException->getMessage());
                }
            }

            Log::error('Registration error : ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withError("Error: " . $e->getMessage());
        }
    }

    /**
     * Validate BayarCash gateway before creating the user
     */
    protected function validateBayarCashGateway()
    {
        try {
            // Get the BayarCash service
            $bayarCashService = app(BayarCashService::class);

            // Get available portals to validate the API connection
            $portals = $bayarCashService->getPortals();

            if (!$portals || empty($portals)) {
                return [
                    'success' => false,
                    'message' => 'Unable to connect to BayarCash payment gateway. Please try again later or choose a different payment method.'
                ];
            }

            // Find the portal key in the available portals
            $portalKey = config('services.bayarcash.portal_key');
            $portalFound = false;

            foreach ($portals as $portal) {
                if (isset($portal->portal_key) && $portal->portal_key === $portalKey) {
                    $portalFound = true;
                    break;
                }
            }

            if (!$portalFound) {
                return [
                    'success' => false,
                    'message' => 'Invalid BayarCash portal key. Please try again later or choose a different payment method.'
                ];
            }

            return [
                'success' => true
            ];

        } catch (Exception $e) {
            Log::error('Error validating BayarCash gateway', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Error connecting to BayarCash payment gateway: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process payment through BayarCash
     */
    protected function processBayarCashPayment(Subscription $subscription, User $user, Company $company, $amount)
    {
        try {
            // Create a dedicated log file for BayarCash payment processing
            $bayarcashLog = new \Monolog\Logger('bayarcash_register');
            $bayarcashLog->pushHandler(new \Monolog\Handler\StreamHandler(
                storage_path('logs/bayarcash.log'),
                \Monolog\Logger::DEBUG
            ));

            $bayarcashLog->info('Starting BayarCash payment process for registration', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'amount' => $amount
            ]);

            // Use the portal key from the validation step to avoid multiple API calls
            $portalKey = config('services.bayarcash.portal_key');
            $paymentChannel = 1; // Default payment channel

            // Generate a shorter order number (max 30 chars)
            $shortOrderId = 'REG' . substr($subscription->id, 0, 8);

            // Log the portal key and payment channel we're using
            $bayarcashLog->info('Using BayarCash portal key and payment channel', [
                'portal_key' => $portalKey,
                'payment_channel' => $paymentChannel,
                'order_number' => $shortOrderId
            ]);

            // Verify that the portal key is set
            if (empty($portalKey)) {
                $bayarcashLog->error('BayarCash portal key is not set in configuration');
                throw new Exception('BayarCash portal key is not set in configuration');
            }

            // Prepare data for payment intent
            $data = [
                'portal_key' => $portalKey,
                'order_number' => $shortOrderId,
                'amount' => $amount,
                'payer_name' => $user->username,
                'payer_email' => $user->email,
                'payer_telephone_number' => $user->userDetail->mobile ?? '',
                'callback_url' => route('payment.bayarcash.registration.callback'),
                'return_url' => 'https://88ad-121-122-52-241.ngrok-free.app/payment/bayarcash/registration/return',
                'payment_channel' => $paymentChannel,
                'user_id' => $user->id,
                'company_id' => $company->id,
                'metadata' => [
                    'subscription_id' => $subscription->id,
                    'type' => 'registration'
                ]
            ];

            $bayarcashLog->info('Prepared payment intent data', [
                'order_number' => $shortOrderId,
                'amount' => $amount,
                'callback_url' => route('payment.bayarcash.registration.callback'),
                'return_url' => route('payment.bayarcash.registration.return')
            ]);

            // Create payment intent
            $bayarcashLog->info('Calling BayarCashService->createPaymentIntent()');
            $result = $this->bayarCashService->createPaymentIntent($data);
            $bayarcashLog->info('Received result from createPaymentIntent()', [
                'success' => $result['success'] ?? false,
                'payment_url' => $result['payment_url'] ?? null,
                'payment_intent_id' => $result['payment_intent_id'] ?? null
            ]);

            if (!$result['success']) {
                // Format error details for better logging
                $errorDetails = '';
                if (isset($result['errors']) && is_array($result['errors'])) {
                    foreach ($result['errors'] as $field => $error) {
                        $errorDetails .= "{$field}: {$error}; ";
                    }
                }

                $bayarcashLog->error('Failed to create BayarCash payment intent', [
                    'error' => $result['message'] ?? 'Unknown error',
                    'error_details' => $errorDetails,
                    'subscription_id' => $subscription->id
                ]);

                Log::error('Failed to create BayarCash payment intent', [
                    'error' => $result['message'] ?? 'Unknown error',
                    'error_details' => $errorDetails,
                    'subscription_id' => $subscription->id
                ]);

                // Log the user out
                auth()->logout();

                // Delete the user and related records properly
                $bayarcashLog->info('Deleting user and related records after payment intent creation failure');
                $user->deleteWithRelations();

                // Redirect back to registration page with error
                $errorMessage = "Payment gateway error: " . ($result['message'] ?? 'Unknown error') .
                    (!empty($errorDetails) ? " Details: " . $errorDetails : "");

                $bayarcashLog->info('Redirecting to registration page with error', [
                    'error_message' => $errorMessage
                ]);

                return redirect()->route('register')
                    ->withInput()
                    ->withError($errorMessage);
            }

            // Update subscription with BayarCash payment details
            $bayarcashLog->info('Updating subscription with BayarCash payment details', [
                'bill_id' => $result['payment_intent_id'],
                'bill_type' => 'bayarcash'
            ]);

            $subscription->update([
                'bill_id' => $result['payment_intent_id'],
                'bill_type' => 'bayarcash'
            ]);

            // Store payment URL in session in case we need it later
            session(['bayarcash_payment_url' => $result['payment_url']]);
            $bayarcashLog->info('Stored payment URL in session');

            // Log the successful payment intent creation
            $bayarcashLog->info('Successfully created BayarCash payment intent', [
                'payment_intent_id' => $result['payment_intent_id'],
                'payment_url' => $result['payment_url'],
                'subscription_id' => $subscription->id
            ]);

            Log::info('Successfully created BayarCash payment intent', [
                'payment_intent_id' => $result['payment_intent_id'],
                'payment_url' => $result['payment_url'],
                'subscription_id' => $subscription->id
            ]);

            // Redirect to BayarCash payment page
            $bayarcashLog->info('Redirecting to BayarCash payment page', [
                'payment_url' => $result['payment_url']
            ]);

            return redirect()->away($result['payment_url']);

        } catch (Exception $e) {
            // Create a dedicated log file for BayarCash payment processing errors
            $bayarcashLog = new \Monolog\Logger('bayarcash_register_error');
            $bayarcashLog->pushHandler(new \Monolog\Handler\StreamHandler(
                storage_path('logs/bayarcash.log'),
                \Monolog\Logger::ERROR
            ));

            $bayarcashLog->error('Error processing BayarCash payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'subscription_id' => $subscription->id
            ]);

            Log::error('Error processing BayarCash payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'subscription_id' => $subscription->id
            ]);

            // Log the user out
            auth()->logout();
            $bayarcashLog->info('Logged user out after payment processing error');

            // Delete the user and related records properly if possible
            if (isset($user) && $user) {
                try {
                    $bayarcashLog->info('Attempting to delete user and related records');
                    $user->deleteWithRelations();
                    $bayarcashLog->info('Successfully deleted user and related records');
                } catch (Exception $deleteException) {
                    // Log the deletion error but continue with the original error
                    $bayarcashLog->error('Error deleting user during payment processing: ' . $deleteException->getMessage());
                    Log::error('Error deleting user during payment processing: ' . $deleteException->getMessage());
                }
            }

            // Redirect back to registration page with error
            $errorMessage = "Payment gateway error: " . $e->getMessage();
            $bayarcashLog->info('Redirecting to registration page with error', [
                'error_message' => $errorMessage
            ]);

            return redirect()->route('register')
                ->withInput()
                ->withError($errorMessage);
        }
    }

    /**
     * Process payment through Bizappay
     */
    protected function processBizappayPayment(Subscription $subscription, User $user, $amount)
    {
        try {
            // Get Bizappay token
            $bizappayController = new BizappayController();
            $bizappayToken = $bizappayController->generateToken();

            // Get API key and URL based on environment
            $getApiKey = config('services.bizappay.key');
            $bizappayUrl = config('services.bizappay.url');
            $categoryCode = config('services.bizappay.categoryCode', 'e2audfn6');

            // Create bill in Bizappay
            $response = Http::withHeaders([
                'Authentication' => $bizappayToken,
            ])
            ->asForm()
            ->post($bizappayUrl . 'bill/create', [
                'apiKey' => $getApiKey,
                'category' => $categoryCode,
                'name' => 'Bizappos Registration',
                'amount' => $amount,
                'payer_name' => $user->username,
                'payer_email' => $user->email,
                'payer_phone' => $user->userDetail->mobile ?? '',
                'webreturn_url' => route('payment.subscription.return'),
                'callback_url' => route('payment.subscription.callback'),
                'reference_1' => $subscription->id,
            ]);

            $bizappayPage = $response->json();

            if ($bizappayPage['status'] == "ok") {
                // Save billcode to subscription
                $subscription->update([
                    'bill_id' => $bizappayPage['billCode'],
                    'bill_type' => 'bizappay'
                ]);

                // Create Bizappay record
                Bizappay::create([
                    'billcode' => $bizappayPage['billCode'],
                    'categorycode' => $categoryCode,
                    'paidamount' => $amount,
                    'paymentstatus' => 'Pending'
                ]);

                // Store payment URL in session in case we need it later
                session(['bizappay_payment_url' => $bizappayPage['url']]);

                // Log the successful bill creation
                Log::info('Successfully created Bizappay bill', [
                    'billcode' => $bizappayPage['billCode'],
                    'payment_url' => $bizappayPage['url'],
                    'subscription_id' => $subscription->id
                ]);

                // Redirect to Bizappay payment page
                return redirect($bizappayPage['url']);
            } else {
                Log::error('Failed to create Bizappay bill', [
                    'response' => $bizappayPage,
                    'subscription_id' => $subscription->id
                ]);

                // Log the user out
                auth()->logout();

                // Delete the user and related records properly
                $user->deleteWithRelations();

                // Redirect back to registration page with error
                return redirect()->route('register')
                    ->withInput()
                    ->withError("Payment gateway error: Failed to create payment bill. Please try again later or contact customer support.");
            }

        } catch (Exception $e) {
            Log::error('Error processing Bizappay payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'subscription_id' => $subscription->id
            ]);

            // Log the user out
            auth()->logout();

            // Delete the user and related records properly if possible
            if (isset($user) && $user) {
                try {
                    $user->deleteWithRelations();
                } catch (Exception $deleteException) {
                    // Log the deletion error but continue with the original error
                    Log::error('Error deleting user during payment processing: ' . $deleteException->getMessage());
                }
            }

            // Redirect back to registration page with error
            return redirect()->route('register')
                ->withInput()
                ->withError("Payment gateway error: " . $e->getMessage());
        }
    }

    /**
     * Map BayarCash status code to internal status
     *
     * @param int|string $statusCode
     * @return string
     */
    private function mapBayarCashStatus($statusCode)
    {
        $statusMap = [
            0 => 'new',
            1 => 'pending',
            2 => 'failed',
            3 => 'success',
            4 => 'cancelled'
        ];

        return $statusMap[$statusCode] ?? 'unknown';
    }

    /**
     * Handle BayarCash callback for registration payment
     */
    public function handleBayarCashCallback(Request $request)
    {
        // Create a dedicated log channel for detailed callback debugging
        $callbackLog = new \Monolog\Logger('bayarcash_registration_callback');
        $callbackLog->pushHandler(new \Monolog\Handler\StreamHandler(
            storage_path('logs/bayarcash_callback.log'),
            \Monolog\Logger::DEBUG
        ));

        // Log raw request details before any processing
        $callbackLog->info('Raw BayarCash registration callback received', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'headers' => $request->headers->all(),
            'body' => $request->all(),
            'timestamp' => now()->toDateTimeString()
        ]);

        // Additional debug logging
        $callbackLog->debug('Server variables', $_SERVER);
        $callbackLog->debug('Request content', [
            'content' => $request->getContent(),
            'content_type' => $request->headers->get('Content-Type')
        ]);

        // Additional debug logging
        $callbackLog->debug('Server variables', $_SERVER);
        $callbackLog->debug('Request content', [
            'content' => $request->getContent(),
            'content_type' => $request->headers->get('Content-Type')
        ]);

        // Additional debug logging
        $callbackLog->debug('Server variables', $_SERVER);
        $callbackLog->debug('Request content', [
            'content' => $request->getContent(),
            'content_type' => $request->headers->get('Content-Type')
        ]);

        $callbackData = $request->all();

        Log::info('BayarCash registration callback received', [
            'data' => $callbackData
        ]);

        try {
            // Verify callback data
            $apiSecretKey = config('services.bayarcash.api_secret_key');
            $useSandbox = config('services.bayarcash.sandbox', true);

            // Get base URL based on environment
            $baseUrl = $useSandbox
                ? config('services.bayarcash.sandbox_base_url', 'https://api.console.bayarcash-sandbox.com/v3/')
                : config('services.bayarcash.base_url', 'https://api.console.bayar.cash/v3/');

            // Initialize the BayarCash SDK
            $apiToken = config('services.bayarcash.api_token');
            $bayarcash = new \App\Services\CustomBayarCashSDK($apiToken, $baseUrl);

            // Set API version to v3
            $bayarcash->setApiVersion('v3');

            // Use sandbox environment for testing
            if ($useSandbox) {
                $bayarcash->useSandbox();
            }

            // Verify callback data
            $isValid = $bayarcash->verifyTransactionCallbackData($callbackData, $apiSecretKey);

            if (!$isValid) {
                Log::warning('Invalid BayarCash registration callback data', [
                    'data' => $callbackData
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Invalid callback data'
                ], 400);
            }

            // Find the transaction
            $transaction = BayarCash::where('payment_intent_id', $callbackData['payment_intent_id'] ?? '')->first();

            if (!$transaction) {
                Log::error('BayarCash transaction not found for callback', [
                    'payment_intent_id' => $callbackData['payment_intent_id'] ?? null
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found'
                ], 404);
            }

            // Map BayarCash status code to internal status
            $status = isset($callbackData['status'])
                ? $this->mapBayarCashStatus($callbackData['status'])
                : 'completed';

            // Update transaction status
            $transaction->update([
                'status' => $status,
                'status_code' => $callbackData['status_code'] ?? null,
                'reference_number' => $callbackData['reference_number'] ?? null,
                'transaction_date' => now(),
                'callback_data' => $callbackData
            ]);

            // Get subscription ID from metadata
            $metadata = $transaction->metadata ?? [];
            $subscriptionId = $metadata['subscription_id'] ?? null;

            if (!$subscriptionId) {
                Log::error('Subscription ID not found in transaction metadata', [
                    'transaction_id' => $transaction->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Subscription ID not found'
                ], 404);
            }

            // Find the subscription
            $subscription = Subscription::find($subscriptionId);

            if (!$subscription) {
                Log::error('Subscription not found for BayarCash callback', [
                    'subscription_id' => $subscriptionId
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found'
                ], 404);
            }

            // Update subscription status based on payment status
            // Status code 3 means 'success' according to BayarCash documentation
            if (($callbackData['status'] ?? '') == 3 || ($callbackData['status'] ?? '') === 'success') {
                $subscription->update([
                    'status' => 'active'
                ]);

                Log::info('BayarCash payment successful, subscription activated', [
                    'subscription_id' => $subscription->id,
                    'payment_intent_id' => $callbackData['payment_intent_id'] ?? null,
                    'status' => $callbackData['status'] ?? 'unknown',
                    'mapped_status' => $this->mapBayarCashStatus($callbackData['status'] ?? '')
                ]);
            } else {
                Log::warning('BayarCash payment not completed', [
                    'subscription_id' => $subscription->id,
                    'payment_intent_id' => $callbackData['payment_intent_id'] ?? null,
                    'status' => $callbackData['status'] ?? 'unknown',
                    'mapped_status' => $this->mapBayarCashStatus($callbackData['status'] ?? '')
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Callback processed successfully'
            ]);

        } catch (Exception $e) {
            Log::error('Error processing BayarCash registration callback', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $callbackData
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error processing callback: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle BayarCash return URL for registration payment
     */
    public function handleBayarCashReturn(Request $request)
    {
        \Log::info('BayarCash return URL accessed', [
            'full_url' => $request->fullUrl(),
            'params' => $request->all(),
            'headers' => $request->headers->all()
        ]);
        
        // Create a dedicated log channel for detailed callback debugging
        $callbackLog = new \Monolog\Logger('bayarcash_registration_return');
        $callbackLog->pushHandler(new \Monolog\Handler\StreamHandler(
            storage_path('logs/bayarcash_callback.log'),
            \Monolog\Logger::DEBUG
        ));

        // Log raw request details before any processing
        $callbackLog->info('Raw BayarCash registration return URL accessed', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'headers' => $request->headers->all(),
            'query_params' => $request->query(),
            'body' => $request->all(),
            'timestamp' => now()->toDateTimeString()
        ]);

        $returnData = $request->all();

        Log::info('BayarCash registration return URL accessed', [
            'data' => $returnData
        ]);

        try {
            // Get payment_intent_id from return data
            $paymentIntentId = $returnData['payment_intent_id'] ?? null;

            if (!$paymentIntentId) {
                Log::warning('Payment intent ID not found in BayarCash return data', [
                    'data' => $returnData
                ]);

                return redirect()->route('payment.fail')
                    ->with('error', 'Payment information not found. Please contact customer support.');
            }

            // Find the transaction
            $transaction = BayarCash::where('payment_intent_id', $paymentIntentId)->first();

            if (!$transaction) {
                Log::error('BayarCash transaction not found for return URL', [
                    'payment_intent_id' => $paymentIntentId
                ]);

                return redirect()->route('payment.fail')
                    ->with('error', 'Payment information not found. Please contact customer support.');
            }

            // Get subscription ID from metadata
            $metadata = $transaction->metadata ?? [];
            $subscriptionId = $metadata['subscription_id'] ?? null;

            if (!$subscriptionId) {
                Log::error('Subscription ID not found in transaction metadata', [
                    'transaction_id' => $transaction->id
                ]);

                return redirect()->route('payment.fail')
                    ->with('error', 'Subscription information not found. Please contact customer support.');
            }

            // Find the subscription
            $subscription = Subscription::find($subscriptionId);

            if (!$subscription) {
                Log::error('Subscription not found for BayarCash return URL', [
                    'subscription_id' => $subscriptionId
                ]);

                return redirect()->route('payment.fail')
                    ->with('error', 'Subscription information not found. Please contact customer support.');
            }

            // Check payment status
            $status = $returnData['status'] ?? $transaction->status;

            if ($status === 'completed') {
                return redirect()->route('payment.success')
                    ->with('success', 'Your payment has been processed successfully. Your subscription is now active.');
            } else {
                // Convert to trial plan if payment failed
                try {
                    // Find the trial plan
                    $trialPlan = SubscriptionPlan::where('name', 'like', '%Trial%')->first();

                    if ($trialPlan) {
                        // Calculate trial period
                        $trialDays = $trialPlan->trial_period_days > 0 ? $trialPlan->trial_period_days : 7;
                        $trialEndsAt = now()->addDays($trialDays);

                        // Update subscription to trial
                        $subscription->update([
                            'subscription_plan_id' => $trialPlan->id,
                            'trial_ends_at' => $trialEndsAt,
                            'ends_at' => $trialEndsAt,
                            'status' => 'active'
                        ]);

                        return redirect()->route('payment.fail')
                            ->with('warning', 'Your payment was not successful. We have automatically enrolled you in a ' . $trialDays . '-day trial plan so you can try our services. You can upgrade to a paid plan at any time.');
                    }
                } catch (Exception $e) {
                    Log::error('Error during payment failure handling', [
                        'error' => $e->getMessage(),
                        'subscription_id' => $subscription->id
                    ]);
                }

                return redirect()->route('payment.fail')
                    ->with('error', 'Your payment was not successful. Please try again or contact customer support for assistance.');
            }

        } catch (Exception $e) {
            Log::error('Error processing BayarCash registration return URL', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $returnData
            ]);

            return redirect()->route('payment.fail')
                ->with('error', 'An error occurred while processing your payment. Please contact customer support for assistance.');
        }
    }
}
