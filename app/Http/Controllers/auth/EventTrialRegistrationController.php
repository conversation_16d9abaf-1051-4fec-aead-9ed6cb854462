<?php

namespace App\Http\Controllers\auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserDetail;
use App\Models\Company;
use App\Models\Receipt;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Subscription\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\TrialWelcomeMail;

class EventTrialRegistrationController extends TrialRegistrationController
{
    /**
     * Show the event trial registration form
     */
    public function index()
    {
        return view("backend.auth.event-trial-registration");
    }

    /**
     * Validate a specific step of the registration form
     */
    public function validateStep(Request $request)
    {
        $step = $request->input("step");
        $rules = $this->getValidationRules($step);

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                "success" => false,
                "errors" => $validator->errors(),
            ]);
        }

        return response()->json(["success" => true]);
    }

    /**
     * Check username availability
     */
    public function checkUsername(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "username" => "required|unique:users,username",
        ]);

        return response()->json([
            "available" => !$validator->fails(),
            "message" => $validator->fails()
                ? "Username cannot be used"
                : "Username available",
        ]);
    }

    /**
     * Process the event trial registration
     * Extends the regular trial registration with event-specific settings
     */
    /**
     * Get validation rules for a specific step
     */
    private function getValidationRules($step)
    {
        switch ($step) {
            case 1:
                return [
                    "username" => "required|unique:users,username|min:3|max:50",
                    "first_name" => "required|string|max:100",
                    "last_name" => "required|string|max:100",
                    "email" => "required|email|unique:users,email",
                    "mobile" => "required|string|min:10|max:15",
                    "password" => "required|min:8",
                    "password_confirmation" => "required|same:password",
                ];
            default:
                return [];
        }
    }

    /**
     * Get all validation rules for final submission
     */
    private function getAllValidationRules()
    {
        return array_merge($this->getValidationRules(1));
    }

    public function processTrialRegistration(Request $request)
    {
        // Validate all steps
        $validator = Validator::make(
            $request->all(),
            $this->getAllValidationRules()
        );

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Find the trial plan
            $trialPlan = SubscriptionPlan::where("name", "Starter - Trial")
                ->where("is_active", true)
                ->first();

            if (!$trialPlan) {
                throw new \Exception("Trial plan not found");
            }

            // Create the user with event-specific settings
            $user = User::create([
                "username" => $request->username,
                "email" => $request->email,
                "password" => $request->password,
                "isBizappUser" => "N",
                "access_module" => "connected", // Event-specific: Set access_module to 'connected'
                "registration_source" => "event", // Track the registration source
            ]);

            // Create user details
            $userDetail = UserDetail::create([
                "user_id" => $user->id,
                "first_name" => $request->first_name,
                "last_name" => $request->last_name,
                "mobile" => $request->mobile,
                "address" => $request->address ?? "address",
                "city" => $request->city ?? "city",
                "state" => $request->state ?? "1",
                "postcode" => $request->postcode ?? "0",
                "country" => $request->country ?? "MALAYSIA",
            ]);

            try {
                $company = Company::create([
                    "user_id" => $user->id,
                    "com_name" => $request->first_name . " " . $request->last_name,
                    "com_address" => "address",
                    "com_city" => "city",
                    "com_state" => "1",
                    "com_postcode" => "0",
                    "com_country" => "MALAYSIA",
                    "com_registration_no" => null,
                    "com_mobile" => $request->mobile,
                    "com_email" => $request->email,
                    "com_sst_value" => "0",
                    "account_type" => "individual",
                    "ic_number" => "0",
                    "registration_source" => "event", // Track registration source in company
                ]);

                Receipt::create([
                    "company_id" => $company->id,
                    "user_id" => $user->id,
                    "title" => $company->com_name,
                    "name" => $request->first_name . " " . $request->last_name,
                    "email" => $request->email,
                    "phone" => $request->mobile,
                    "address" => "address",
                    "city" => "city",
                    "state" => "1",
                    "postcode" => "0",
                    "country" => "MALAYSIA",
                    "sst" => "0,0",
                ]);
            } catch (\Exception $e) {
                \Log::error(
                    "Company/Receipt creation failed: " . $e->getMessage()
                );
                return back()->withErrors(
                    "Registration failed. Please try again."
                );
            }

            // Create trial subscription with event tracking
            $subscription = Subscription::create([
                "company_id" => $company->id,
                "subscription_plan_id" => $trialPlan->id,
                "starts_at" => now(),
                "ends_at" => now()->addDays(7),
                "trial_ends_at" => now()->addDays(7),
                "status" => "active",
                "bill_type" => "trial",
                "source" => "event", // Track the subscription source
            ]);

            DB::commit();

            try {
                // Send welcome email
                Mail::to($user->email)->send(
                    new TrialWelcomeMail($user, $company)
                );
            } catch (\Exception $e) {
                Log::error("Failed to send welcome email: " . $e->getMessage());
            }

            // Log the event registration
            Log::info("Event trial registration completed", [
                "user_id" => $user->id,
                "company_id" => $company->id,
                "subscription_id" => $subscription->id,
                "event_registration" => true,
            ]);

            // Log the user in
            auth()->login($user);

            return redirect()
                ->route("dashboard")
                ->with(
                    "success",
                    "Welcome to our special event trial! Your 7-day free trial has started with enhanced connected features. Explore all features and see how Bizappos can help your business grow."
                );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Event trial registration failed", [
                "error" => $e->getMessage(),
                "email" => $request->email,
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->withErrors([
                    "general" => "Registration failed. Please try again.",
                ]);
        }
    }
}
