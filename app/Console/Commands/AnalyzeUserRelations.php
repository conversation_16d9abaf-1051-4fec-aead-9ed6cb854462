<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class AnalyzeUserRelations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:analyze-relations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze user relations distribution to understand data patterns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== USER RELATIONS ANALYSIS ===');
        
        // Basic counts
        $totalUsers = User::count();
        $usersWithCompanies = User::whereHas('companies')->count();
        $usersWithEmployees = User::whereHas('employee')->count();
        $usersWithBoth = User::whereHas('companies')->whereHas('employee')->count();
        $usersWithNeither = User::whereDoesntHave('companies')->whereDoesntHave('employee')->count();
        $usersWithEither = User::where(function($query) {
            $query->whereHas('companies')->orWhereHas('employee');
        })->count();
        
        // User type breakdown
        $bizappUsers = User::where('isBizappUser', 'Y')->count();
        $standaloneUsers = User::where('isBizappUser', '!=', 'Y')->count();
        
        // Bizapp user relations
        $bizappWithCompanies = User::where('isBizappUser', 'Y')->whereHas('companies')->count();
        $bizappWithEmployees = User::where('isBizappUser', 'Y')->whereHas('employee')->count();
        $bizappWithBoth = User::where('isBizappUser', 'Y')->whereHas('companies')->whereHas('employee')->count();
        $bizappWithNeither = User::where('isBizappUser', 'Y')->whereDoesntHave('companies')->whereDoesntHave('employee')->count();
        
        // Standalone user relations
        $standaloneWithCompanies = User::where('isBizappUser', '!=', 'Y')->whereHas('companies')->count();
        $standaloneWithEmployees = User::where('isBizappUser', '!=', 'Y')->whereHas('employee')->count();
        $standaloneWithBoth = User::where('isBizappUser', '!=', 'Y')->whereHas('companies')->whereHas('employee')->count();
        $standaloneWithNeither = User::where('isBizappUser', '!=', 'Y')->whereDoesntHave('companies')->whereDoesntHave('employee')->count();
        
        // CURRENT QUERY (unified business logic)
        $currentQueryCount = User::where(function($query) {
            $query->whereDoesntHave('companies')->orWhereDoesntHave('employee');
        })->count();

        // Users who are fully satisfied (have both relations)
        $fullySatisfiedCount = User::whereHas('companies')->whereHas('employee')->count();
        
        $this->table(['Metric', 'Count', 'Percentage'], [
            ['Total Users', $totalUsers, '100%'],
            ['Users with Companies', $usersWithCompanies, round(($usersWithCompanies/$totalUsers)*100, 1).'%'],
            ['Users with Employee Records', $usersWithEmployees, round(($usersWithEmployees/$totalUsers)*100, 1).'%'],
            ['Users with BOTH Relations', $usersWithBoth, round(($usersWithBoth/$totalUsers)*100, 1).'%'],
            ['Users with EITHER Relation', $usersWithEither, round(($usersWithEither/$totalUsers)*100, 1).'%'],
            ['Users with NEITHER Relation', $usersWithNeither, round(($usersWithNeither/$totalUsers)*100, 1).'%'],
        ]);
        
        $this->info('');
        $this->info('=== USER TYPE BREAKDOWN ===');
        $this->table(['User Type', 'Total', 'With Companies', 'With Employees', 'With Both', 'With Neither'], [
            [
                'Bizapp Users', 
                $bizappUsers, 
                $bizappWithCompanies, 
                $bizappWithEmployees, 
                $bizappWithBoth, 
                $bizappWithNeither
            ],
            [
                'Standalone Users', 
                $standaloneUsers, 
                $standaloneWithCompanies, 
                $standaloneWithEmployees, 
                $standaloneWithBoth, 
                $standaloneWithNeither
            ]
        ]);
        
        $this->info('');
        $this->info('=== CURRENT BUSINESS LOGIC ===');
        $this->table(['Query Type', 'Count', 'Percentage of Total'], [
            ['Users needing processing (missing either relation)', $currentQueryCount, round(($currentQueryCount/$totalUsers)*100, 1).'%'],
            ['Users fully satisfied (have both relations)', $fullySatisfiedCount, round(($fullySatisfiedCount/$totalUsers)*100, 1).'%'],
        ]);

        $this->info('');
        $this->info('✅ UNIFIED BUSINESS LOGIC: Both Bizapp and Standalone users need EITHER company OR employee relation');
        $percentage = round(($currentQueryCount/$totalUsers)*100, 1);
        $this->info("   Current query returns {$currentQueryCount} users ({$percentage}% of total) for processing");

        if ($currentQueryCount > ($totalUsers * 0.8)) {
            $this->warn("⚠️  High processing volume: This will process most users in the system");
        } elseif ($currentQueryCount > ($totalUsers * 0.5)) {
            $this->info("ℹ️  Moderate processing volume: This will process about half the users");
        } else {
            $this->info("✅ Reasonable processing volume: This will process a subset of users");
        }
        
        $this->info('');
        $this->info('=== BUSINESS LOGIC SUMMARY ===');
        $this->info('• Both Bizapp and Standalone users need EITHER company OR employee relation');
        $this->info('• Bizapp users: Data comes from Bizapp API (boss gets company, staff gets employee)');
        $this->info('• Standalone users: Data uses local defaults (preference for company creation)');

        $this->info('');
        $this->info('=== USERS NEEDING PROCESSING ===');
        if ($bizappWithNeither > 0) {
            $this->warn("• {$bizappWithNeither} Bizapp users have no relations (will call API)");
        }
        if ($standaloneWithNeither > 0) {
            $this->warn("• {$standaloneWithNeither} Standalone users have no relations (will create company)");
        }

        $bizappNeedingEither = User::where('isBizappUser', 'Y')->where(function($query) {
            $query->whereDoesntHave('companies')->orWhereDoesntHave('employee');
        })->count();
        $standaloneNeedingEither = User::where('isBizappUser', '!=', 'Y')->where(function($query) {
            $query->whereDoesntHave('companies')->orWhereDoesntHave('employee');
        })->count();

        if ($bizappNeedingEither > $bizappWithNeither) {
            $this->info("• " . ($bizappNeedingEither - $bizappWithNeither) . " additional Bizapp users need either company or employee");
        }
        if ($standaloneNeedingEither > $standaloneWithNeither) {
            $this->info("• " . ($standaloneNeedingEither - $standaloneWithNeither) . " additional Standalone users need either company or employee");
        }
        
        return 0;
    }
}
