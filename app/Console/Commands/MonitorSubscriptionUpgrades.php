<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class MonitorSubscriptionUpgrades extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:monitor-subscription-upgrades 
                            {--refresh=5 : Refresh interval in seconds}
                            {--alerts : Enable alerts for critical events}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Real-time monitoring of subscription upgrade and e-mandate processes';

    private $lastPosition = 0;
    private $alertThresholds = [
        'error_rate' => 10, // errors per minute
        'misrouted_status' => 5, // misrouted status=0 per hour
        'failed_enrollments' => 3, // failed enrollments per 10 minutes
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting Real-time Subscription Upgrade Monitoring');
        $this->info('Press Ctrl+C to stop monitoring');
        $this->newLine();

        $refreshInterval = (int) $this->option('refresh');
        $enableAlerts = $this->option('alerts');

        $logFile = storage_path('logs/subscription-upgrade-emandate-' . date('Y-m-d') . '.log');
        
        if (!File::exists($logFile)) {
            $this->warn("Log file not found: {$logFile}");
            $this->info('Waiting for log file to be created...');
        }

        $this->lastPosition = File::exists($logFile) ? File::size($logFile) : 0;

        while (true) {
            $this->clearScreen();
            $this->displayHeader();
            
            $newEntries = $this->readNewLogEntries($logFile);
            
            if (!empty($newEntries)) {
                $this->displayRecentActivity($newEntries);
                
                if ($enableAlerts) {
                    $this->checkAlerts($newEntries);
                }
            }

            $this->displayStatistics($logFile);
            $this->displaySystemStatus();
            
            $this->info("Last updated: " . Carbon::now()->format('Y-m-d H:i:s') . " | Refresh: {$refreshInterval}s");
            
            sleep($refreshInterval);
        }
    }

    /**
     * Clear the screen for real-time updates
     */
    private function clearScreen(): void
    {
        if (PHP_OS_FAMILY === 'Windows') {
            system('cls');
        } else {
            system('clear');
        }
    }

    /**
     * Display monitoring header
     */
    private function displayHeader(): void
    {
        $this->info('╔══════════════════════════════════════════════════════════════╗');
        $this->info('║              SUBSCRIPTION UPGRADE MONITOR                   ║');
        $this->info('║                  BayarCash E-Mandate                        ║');
        $this->info('╚══════════════════════════════════════════════════════════════╝');
        $this->newLine();
    }

    /**
     * Read new log entries since last check
     */
    private function readNewLogEntries(string $logFile): array
    {
        if (!File::exists($logFile)) {
            return [];
        }

        $currentSize = File::size($logFile);
        
        if ($currentSize <= $this->lastPosition) {
            return [];
        }

        $handle = fopen($logFile, 'r');
        fseek($handle, $this->lastPosition);
        
        $newContent = fread($handle, $currentSize - $this->lastPosition);
        fclose($handle);

        $this->lastPosition = $currentSize;

        $lines = array_filter(explode("\n", $newContent));
        $entries = [];

        foreach ($lines as $line) {
            $entry = $this->parseLogEntry($line);
            if ($entry) {
                $entries[] = $entry;
            }
        }

        return $entries;
    }

    /**
     * Parse a single log entry
     */
    private function parseLogEntry(string $line): ?array
    {
        if (!preg_match('/\[(.*?)\] .*?\.(.*?): (.*?) (\{.*\}) (\{.*\})/', $line, $matches)) {
            return null;
        }

        $context = json_decode($matches[4], true);
        $extra = json_decode($matches[5], true);

        return [
            'timestamp' => $matches[1],
            'level' => $matches[2],
            'message' => $matches[3],
            'context' => $context ?: [],
            'extra' => $extra ?: [],
            'event' => $context['event'] ?? 'unknown',
        ];
    }

    /**
     * Display recent activity
     */
    private function displayRecentActivity(array $entries): void
    {
        $this->info('🔥 Recent Activity (' . count($entries) . ' new entries)');
        $this->line('─────────────────────────────────────────────────────');

        $recentEntries = array_slice(array_reverse($entries), 0, 10);

        foreach ($recentEntries as $entry) {
            $time = Carbon::parse($entry['timestamp'])->format('H:i:s');
            $level = $this->formatLevel($entry['level']);
            $event = $entry['event'];
            $message = substr($entry['message'], 0, 50) . (strlen($entry['message']) > 50 ? '...' : '');

            $this->line("{$time} {$level} [{$event}] {$message}");
        }

        $this->newLine();
    }

    /**
     * Format log level with colors
     */
    private function formatLevel(string $level): string
    {
        switch ($level) {
            case 'ERROR':
                return '<fg=red>ERROR</>';
            case 'WARNING':
                return '<fg=yellow>WARN </>';
            case 'INFO':
                return '<fg=green>INFO </>';
            case 'DEBUG':
                return '<fg=blue>DEBUG</>';
            default:
                return $level;
        }
    }

    /**
     * Check for alert conditions
     */
    private function checkAlerts(array $entries): void
    {
        $errors = array_filter($entries, fn($e) => $e['level'] === 'ERROR');
        $misroutedStatus = array_filter($entries, fn($e) => $e['event'] === 'misrouted_pending_status_detected');
        $failedEnrollments = array_filter($entries, fn($e) => $e['event'] === 'emandate_enrollment_failed');

        if (count($errors) >= $this->alertThresholds['error_rate']) {
            $this->error("🚨 ALERT: High error rate detected! {count($errors)} errors in last refresh");
        }

        if (count($misroutedStatus) >= $this->alertThresholds['misrouted_status']) {
            $this->error("🚨 ALERT: Multiple status=0 misrouting detected! {count($misroutedStatus)} cases");
        }

        if (count($failedEnrollments) >= $this->alertThresholds['failed_enrollments']) {
            $this->error("🚨 ALERT: Multiple enrollment failures! {count($failedEnrollments)} failures");
        }
    }

    /**
     * Display current statistics
     */
    private function displayStatistics(string $logFile): void
    {
        $this->info('📊 Today\'s Statistics');
        $this->line('─────────────────────');

        if (!File::exists($logFile)) {
            $this->warn('No log file found for today');
            return;
        }

        $allEntries = $this->getAllTodayEntries($logFile);
        
        $stats = [
            'total_entries' => count($allEntries),
            'upgrades_initiated' => count(array_filter($allEntries, fn($e) => $e['event'] === 'upgrade_initiated')),
            'upgrades_completed' => count(array_filter($allEntries, fn($e) => $e['event'] === 'upgrade_completed')),
            'upgrades_failed' => count(array_filter($allEntries, fn($e) => $e['event'] === 'upgrade_rollback')),
            'errors' => count(array_filter($allEntries, fn($e) => $e['level'] === 'ERROR')),
            'warnings' => count(array_filter($allEntries, fn($e) => $e['level'] === 'WARNING')),
            'misrouted_status' => count(array_filter($allEntries, fn($e) => $e['event'] === 'misrouted_pending_status_detected')),
        ];

        $successRate = $stats['upgrades_initiated'] > 0 ? 
            round(($stats['upgrades_completed'] / $stats['upgrades_initiated']) * 100, 1) : 0;

        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Log Entries', number_format($stats['total_entries'])],
                ['Upgrades Initiated', number_format($stats['upgrades_initiated'])],
                ['Upgrades Completed', number_format($stats['upgrades_completed'])],
                ['Upgrades Failed', number_format($stats['upgrades_failed'])],
                ['Success Rate', "{$successRate}%"],
                ['Errors', number_format($stats['errors'])],
                ['Warnings', number_format($stats['warnings'])],
                ['Status=0 Misrouted', number_format($stats['misrouted_status'])],
            ]
        );

        $this->newLine();
    }

    /**
     * Get all entries from today's log file
     */
    private function getAllTodayEntries(string $logFile): array
    {
        $lines = File::lines($logFile);
        $entries = [];

        foreach ($lines as $line) {
            $entry = $this->parseLogEntry($line);
            if ($entry) {
                $entries[] = $entry;
            }
        }

        return $entries;
    }

    /**
     * Display system status
     */
    private function displaySystemStatus(): void
    {
        $this->info('🖥️  System Status');
        $this->line('─────────────────');

        $logFile = storage_path('logs/subscription-upgrade-emandate-' . date('Y-m-d') . '.log');
        $logSize = File::exists($logFile) ? File::size($logFile) : 0;
        $logSizeFormatted = $this->formatBytes($logSize);

        $diskFree = disk_free_space(storage_path('logs'));
        $diskFreeFormatted = $this->formatBytes($diskFree);

        $memoryUsage = memory_get_usage(true);
        $memoryUsageFormatted = $this->formatBytes($memoryUsage);

        $this->table(
            ['Component', 'Status'],
            [
                ['Log File Size', $logSizeFormatted],
                ['Disk Space Free', $diskFreeFormatted],
                ['Memory Usage', $memoryUsageFormatted],
                ['Log File Path', $logFile],
                ['Monitoring Since', Carbon::now()->subSeconds($this->lastPosition > 0 ? 60 : 0)->format('H:i:s')],
            ]
        );
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;

        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }

        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
}
