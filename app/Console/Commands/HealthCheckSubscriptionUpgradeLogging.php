<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use App\Services\SubscriptionUpgradeLogger;
use Carbon\Carbon;

class HealthCheckSubscriptionUpgradeLogging extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:health-check-subscription-upgrades
                            {--fix : Attempt to fix detected issues}
                            {--detailed : Show detailed output}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Health check for subscription upgrade logging system';

    private $issues = [];
    private $warnings = [];
    private $successes = [];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🏥 Subscription Upgrade Logging Health Check');
        $this->info('═══════════════════════════════════════════');
        $this->newLine();

        $this->checkLogDirectory();
        $this->checkLogConfiguration();
        $this->checkLogChannel();
        $this->checkLogRotation();
        $this->checkDiskSpace();
        $this->checkLogPermissions();
        $this->checkLoggerService();
        $this->testLogging();

        $this->displayResults();

        if ($this->option('fix') && !empty($this->issues)) {
            $this->attemptFixes();
        }

        return empty($this->issues) ? 0 : 1;
    }

    /**
     * Check log directory exists and is writable
     */
    private function checkLogDirectory(): void
    {
        $logDir = storage_path('logs');
        
        if (!File::exists($logDir)) {
            $this->issues[] = "Log directory does not exist: {$logDir}";
            return;
        }

        if (!File::isWritable($logDir)) {
            $this->issues[] = "Log directory is not writable: {$logDir}";
            return;
        }

        $this->successes[] = "Log directory exists and is writable";
        
        if ($this->option('detailed')) {
            $this->info("✅ Log directory: {$logDir}");
        }
    }

    /**
     * Check logging configuration
     */
    private function checkLogConfiguration(): void
    {
        $config = config('logging.channels.subscription_upgrade_emandate');
        
        if (!$config) {
            $this->issues[] = "Subscription upgrade e-mandate log channel not configured";
            return;
        }

        $requiredKeys = ['driver', 'path', 'level', 'days'];
        $missingKeys = array_diff($requiredKeys, array_keys($config));
        
        if (!empty($missingKeys)) {
            $this->issues[] = "Missing configuration keys: " . implode(', ', $missingKeys);
            return;
        }

        $this->successes[] = "Log channel configuration is complete";
        
        if ($this->option('detailed')) {
            $this->info("✅ Log channel configured with driver: {$config['driver']}");
            $this->info("   Path: {$config['path']}");
            $this->info("   Level: {$config['level']}");
            $this->info("   Retention: {$config['days']} days");
        }
    }

    /**
     * Check if log channel is accessible
     */
    private function checkLogChannel(): void
    {
        try {
            $logger = Log::channel('subscription_upgrade_emandate');
            $this->successes[] = "Log channel is accessible";
            
            if ($this->option('detailed')) {
                $this->info("✅ Log channel accessible");
            }
        } catch (\Exception $e) {
            $this->issues[] = "Cannot access log channel: " . $e->getMessage();
        }
    }

    /**
     * Check log rotation and old files
     */
    private function checkLogRotation(): void
    {
        $logPattern = storage_path('logs/subscription-upgrade-emandate-*.log');
        $logFiles = glob($logPattern);
        
        if (empty($logFiles)) {
            $this->warnings[] = "No subscription upgrade log files found";
            return;
        }

        $oldFiles = [];
        $retentionDays = config('logging.channels.subscription_upgrade_emandate.days', 30);
        $cutoffDate = Carbon::now()->subDays($retentionDays);

        foreach ($logFiles as $file) {
            $fileDate = $this->extractDateFromLogFile($file);
            if ($fileDate && $fileDate->lt($cutoffDate)) {
                $oldFiles[] = $file;
            }
        }

        if (!empty($oldFiles)) {
            $this->warnings[] = count($oldFiles) . " log files older than {$retentionDays} days found";
        } else {
            $this->successes[] = "Log rotation is working correctly";
        }

        if ($this->option('detailed')) {
            $this->info("✅ Found " . count($logFiles) . " log files");
            if (!empty($oldFiles)) {
                $this->warn("⚠️  Old files: " . implode(', ', array_map('basename', $oldFiles)));
            }
        }
    }

    /**
     * Extract date from log filename
     */
    private function extractDateFromLogFile(string $filename): ?Carbon
    {
        if (preg_match('/subscription-upgrade-emandate-(\d{4}-\d{2}-\d{2})\.log/', $filename, $matches)) {
            try {
                return Carbon::createFromFormat('Y-m-d', $matches[1]);
            } catch (\Exception $e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Check available disk space
     */
    private function checkDiskSpace(): void
    {
        $logDir = storage_path('logs');
        $freeBytes = disk_free_space($logDir);
        $totalBytes = disk_total_space($logDir);
        
        if ($freeBytes === false || $totalBytes === false) {
            $this->warnings[] = "Cannot determine disk space";
            return;
        }

        $freeGB = $freeBytes / (1024 * 1024 * 1024);
        $usagePercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;

        if ($freeGB < 1) {
            $this->issues[] = "Low disk space: " . round($freeGB, 2) . " GB free";
        } elseif ($usagePercent > 90) {
            $this->warnings[] = "High disk usage: " . round($usagePercent, 1) . "%";
        } else {
            $this->successes[] = "Sufficient disk space available";
        }

        if ($this->option('detailed')) {
            $this->info("✅ Disk space: " . round($freeGB, 2) . " GB free (" . round(100 - $usagePercent, 1) . "% available)");
        }
    }

    /**
     * Check log file permissions
     */
    private function checkLogPermissions(): void
    {
        $todayLogFile = storage_path('logs/subscription-upgrade-emandate-' . date('Y-m-d') . '.log');
        
        if (File::exists($todayLogFile)) {
            $permissions = substr(sprintf('%o', fileperms($todayLogFile)), -4);
            
            if ($permissions !== '0664' && $permissions !== '0644') {
                $this->warnings[] = "Log file permissions may be incorrect: {$permissions}";
            } else {
                $this->successes[] = "Log file permissions are correct";
            }

            if ($this->option('detailed')) {
                $this->info("✅ Log file permissions: {$permissions}");
            }
        } else {
            $this->warnings[] = "Today's log file does not exist yet";
        }
    }

    /**
     * Check if SubscriptionUpgradeLogger service is working
     */
    private function checkLoggerService(): void
    {
        try {
            $reflection = new \ReflectionClass(SubscriptionUpgradeLogger::class);
            $methods = $reflection->getMethods(\ReflectionMethod::IS_PUBLIC | \ReflectionMethod::IS_STATIC);
            
            $expectedMethods = [
                'logUpgradeInitiation',
                'logUpgradeCompletion',
                'logEMandateEnrollmentAttempt',
                'logError'
            ];

            $missingMethods = array_diff($expectedMethods, array_map(fn($m) => $m->getName(), $methods));
            
            if (!empty($missingMethods)) {
                $this->issues[] = "SubscriptionUpgradeLogger missing methods: " . implode(', ', $missingMethods);
            } else {
                $this->successes[] = "SubscriptionUpgradeLogger service is complete";
            }

            if ($this->option('detailed')) {
                $this->info("✅ SubscriptionUpgradeLogger has " . count($methods) . " methods");
            }
        } catch (\Exception $e) {
            $this->issues[] = "Cannot analyze SubscriptionUpgradeLogger: " . $e->getMessage();
        }
    }

    /**
     * Test actual logging functionality
     */
    private function testLogging(): void
    {
        try {
            $testMessage = "Health check test - " . Carbon::now()->toISOString();
            
            Log::channel('subscription_upgrade_emandate')->info($testMessage, [
                'event' => 'health_check_test',
                'test_id' => uniqid('test_'),
                'timestamp' => Carbon::now()->toISOString()
            ]);

            // Wait a moment and check if the log was written
            sleep(1);
            
            $todayLogFile = storage_path('logs/subscription-upgrade-emandate-' . date('Y-m-d') . '.log');
            
            if (File::exists($todayLogFile)) {
                $logContent = File::get($todayLogFile);
                if (strpos($logContent, $testMessage) !== false) {
                    $this->successes[] = "Test logging successful";
                } else {
                    $this->issues[] = "Test log entry not found in log file";
                }
            } else {
                $this->issues[] = "Log file was not created during test";
            }

            if ($this->option('detailed')) {
                $this->info("✅ Test log entry written and verified");
            }
        } catch (\Exception $e) {
            $this->issues[] = "Test logging failed: " . $e->getMessage();
        }
    }

    /**
     * Display health check results
     */
    private function displayResults(): void
    {
        $this->newLine();
        $this->info('📋 Health Check Results');
        $this->line('═══════════════════════');

        if (!empty($this->successes)) {
            $this->info('✅ Successes (' . count($this->successes) . ')');
            foreach ($this->successes as $success) {
                $this->line("   • {$success}");
            }
            $this->newLine();
        }

        if (!empty($this->warnings)) {
            $this->warn('⚠️  Warnings (' . count($this->warnings) . ')');
            foreach ($this->warnings as $warning) {
                $this->line("   • {$warning}");
            }
            $this->newLine();
        }

        if (!empty($this->issues)) {
            $this->error('❌ Issues (' . count($this->issues) . ')');
            foreach ($this->issues as $issue) {
                $this->line("   • {$issue}");
            }
            $this->newLine();
        }

        // Overall status
        if (empty($this->issues)) {
            $this->info('🎉 Overall Status: HEALTHY');
        } else {
            $this->error('🚨 Overall Status: ISSUES DETECTED');
        }
    }

    /**
     * Attempt to fix detected issues
     */
    private function attemptFixes(): void
    {
        $this->newLine();
        $this->info('🔧 Attempting to fix issues...');
        $this->line('─────────────────────────────');

        foreach ($this->issues as $issue) {
            if (strpos($issue, 'Log directory does not exist') !== false) {
                $this->fixLogDirectory();
            } elseif (strpos($issue, 'Log directory is not writable') !== false) {
                $this->fixLogPermissions();
            } elseif (strpos($issue, 'log files older than') !== false) {
                $this->fixOldLogFiles();
            }
        }
    }

    /**
     * Fix log directory
     */
    private function fixLogDirectory(): void
    {
        $logDir = storage_path('logs');
        
        try {
            File::makeDirectory($logDir, 0755, true);
            $this->info("✅ Created log directory: {$logDir}");
        } catch (\Exception $e) {
            $this->error("❌ Failed to create log directory: " . $e->getMessage());
        }
    }

    /**
     * Fix log permissions
     */
    private function fixLogPermissions(): void
    {
        $logDir = storage_path('logs');
        
        try {
            chmod($logDir, 0755);
            $this->info("✅ Fixed log directory permissions");
        } catch (\Exception $e) {
            $this->error("❌ Failed to fix permissions: " . $e->getMessage());
        }
    }

    /**
     * Fix old log files
     */
    private function fixOldLogFiles(): void
    {
        $logPattern = storage_path('logs/subscription-upgrade-emandate-*.log');
        $logFiles = glob($logPattern);
        $retentionDays = config('logging.channels.subscription_upgrade_emandate.days', 30);
        $cutoffDate = Carbon::now()->subDays($retentionDays);
        $deletedCount = 0;

        foreach ($logFiles as $file) {
            $fileDate = $this->extractDateFromLogFile($file);
            if ($fileDate && $fileDate->lt($cutoffDate)) {
                try {
                    File::delete($file);
                    $deletedCount++;
                } catch (\Exception $e) {
                    $this->error("❌ Failed to delete old log file: " . basename($file));
                }
            }
        }

        if ($deletedCount > 0) {
            $this->info("✅ Deleted {$deletedCount} old log files");
        }
    }
}
