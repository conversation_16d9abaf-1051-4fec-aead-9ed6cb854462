<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckBayarCashConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bayarcash:check-config';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check BayarCash configuration and environment variables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking BayarCash Configuration...');
        $this->newLine();

        // Check environment variables
        $envVars = [
            'BC_API_TOKEN' => env('BC_API_TOKEN'),
            'BC_API_SECRET_KEY' => env('BC_API_SECRET_KEY'),
            'BC_PORTAL_KEY' => env('BC_PORTAL_KEY'),
            'BC_SANDBOX' => env('BC_SANDBOX'),
            'BC_API_VERSION' => env('BC_API_VERSION'),
            'BC_BASE_URL' => env('BC_BASE_URL'),
            'BC_SANDBOX_BASE_URL' => env('BC_SANDBOX_BASE_URL'),
        ];

        $this->info('Environment Variables:');
        foreach ($envVars as $key => $value) {
            $status = $value ? '✓ SET' : '✗ NOT SET';
            $displayValue = $value ? (strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value) : 'null';
            $this->line("  {$key}: {$status} ({$displayValue})");
        }

        $this->newLine();

        // Check config values
        $configVars = [
            'services.bayarcash.api_token' => config('services.bayarcash.api_token'),
            'services.bayarcash.api_secret_key' => config('services.bayarcash.api_secret_key'),
            'services.bayarcash.portal_key' => config('services.bayarcash.portal_key'),
            'services.bayarcash.sandbox' => config('services.bayarcash.sandbox'),
            'services.bayarcash.api_version' => config('services.bayarcash.api_version'),
            'services.bayarcash.base_url' => config('services.bayarcash.base_url'),
            'services.bayarcash.sandbox_base_url' => config('services.bayarcash.sandbox_base_url'),
        ];

        $this->info('Config Values:');
        foreach ($configVars as $key => $value) {
            $status = $value ? '✓ SET' : '✗ NOT SET';
            $displayValue = $value ? (strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value) : 'null';
            $this->line("  {$key}: {$status} ({$displayValue})");
        }

        $this->newLine();

        // Check critical issues
        $issues = [];
        if (empty(config('services.bayarcash.api_token'))) {
            $issues[] = 'API Token is missing - this will cause the CustomBayarCashSDK to fail';
        }
        if (empty(config('services.bayarcash.api_secret_key'))) {
            $issues[] = 'API Secret Key is missing - this will cause checksum validation to fail';
        }
        if (empty(config('services.bayarcash.portal_key'))) {
            $issues[] = 'Portal Key is missing - this will cause payment requests to fail';
        }

        if (!empty($issues)) {
            $this->error('Critical Issues Found:');
            foreach ($issues as $issue) {
                $this->line("  ✗ {$issue}");
            }
        } else {
            $this->info('✓ All critical configuration values are set');
        }

        $this->newLine();

        // Test SDK initialization
        $this->info('Testing SDK Initialization...');
        try {
            $apiToken = config('services.bayarcash.api_token');
            $baseUrl = config('services.bayarcash.sandbox', true)
                ? config('services.bayarcash.sandbox_base_url')
                : config('services.bayarcash.base_url');

            if (empty($apiToken)) {
                $this->error('✗ Cannot test SDK - API token is missing');
            } else {
                $sdk = new \App\Services\CustomBayarCashSDK($apiToken, $baseUrl);
                $this->info('✓ CustomBayarCashSDK initialized successfully');
            }
        } catch (\Exception $e) {
            $this->error("✗ SDK initialization failed: {$e->getMessage()}");
        }

        $this->newLine();
        $this->info('Configuration check complete.');
    }
}
