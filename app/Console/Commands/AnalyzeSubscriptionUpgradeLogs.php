<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class AnalyzeSubscriptionUpgradeLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:analyze-subscription-upgrades 
                            {--date= : Specific date to analyze (YYYY-MM-DD)}
                            {--days=1 : Number of days to analyze}
                            {--format=table : Output format (table, json, csv)}
                            {--filter= : Filter by event type}
                            {--user= : Filter by user ID}
                            {--subscription= : Filter by subscription ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze subscription upgrade and e-mandate logs for monitoring and debugging';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Analyzing Subscription Upgrade E-Mandate Logs');
        $this->newLine();

        $dates = $this->getAnalysisDates();
        $logData = $this->collectLogData($dates);

        if (empty($logData)) {
            $this->warn('No log data found for the specified criteria.');
            return;
        }

        $this->displaySummary($logData);
        $this->displayEventBreakdown($logData);
        $this->displayErrorAnalysis($logData);
        $this->displayStatusCodeAnalysis($logData);
        $this->displayPerformanceMetrics($logData);

        if ($this->option('format') === 'json') {
            $this->outputJson($logData);
        } elseif ($this->option('format') === 'csv') {
            $this->outputCsv($logData);
        }
    }

    /**
     * Get the dates to analyze based on options
     */
    private function getAnalysisDates(): array
    {
        $dates = [];
        
        if ($this->option('date')) {
            $dates[] = $this->option('date');
        } else {
            $days = (int) $this->option('days');
            for ($i = 0; $i < $days; $i++) {
                $dates[] = Carbon::now()->subDays($i)->format('Y-m-d');
            }
        }

        return $dates;
    }

    /**
     * Collect log data from files
     */
    private function collectLogData(array $dates): array
    {
        $logData = [];

        foreach ($dates as $date) {
            $logFile = storage_path("logs/subscription-upgrade-emandate-{$date}.log");
            
            if (!File::exists($logFile)) {
                $this->warn("Log file not found for date: {$date}");
                continue;
            }

            $this->info("📖 Reading log file: {$date}");
            
            $lines = File::lines($logFile);
            foreach ($lines as $line) {
                $entry = $this->parseLogEntry($line);
                if ($entry && $this->matchesFilters($entry)) {
                    $logData[] = $entry;
                }
            }
        }

        return $logData;
    }

    /**
     * Parse a single log entry
     */
    private function parseLogEntry(string $line): ?array
    {
        // Parse Laravel log format: [timestamp] channel.level: message context extra
        if (!preg_match('/\[(.*?)\] .*?\.(.*?): (.*?) (\{.*\}) (\{.*\})/', $line, $matches)) {
            return null;
        }

        $context = json_decode($matches[4], true);
        $extra = json_decode($matches[5], true);

        return [
            'timestamp' => $matches[1],
            'level' => $matches[2],
            'message' => $matches[3],
            'context' => $context ?: [],
            'extra' => $extra ?: [],
            'event' => $context['event'] ?? 'unknown',
            'user_id' => $extra['user_id'] ?? $context['user_id'] ?? null,
            'subscription_id' => $context['subscription_id'] ?? null,
        ];
    }

    /**
     * Check if entry matches filters
     */
    private function matchesFilters(array $entry): bool
    {
        if ($this->option('filter') && $entry['event'] !== $this->option('filter')) {
            return false;
        }

        if ($this->option('user') && $entry['user_id'] != $this->option('user')) {
            return false;
        }

        if ($this->option('subscription') && $entry['subscription_id'] !== $this->option('subscription')) {
            return false;
        }

        return true;
    }

    /**
     * Display summary statistics
     */
    private function displaySummary(array $logData): void
    {
        $this->info('📊 Summary Statistics');
        $this->line('─────────────────────');

        $totalEntries = count($logData);
        $uniqueUsers = count(array_unique(array_filter(array_column($logData, 'user_id'))));
        $uniqueSubscriptions = count(array_unique(array_filter(array_column($logData, 'subscription_id'))));

        $levelCounts = array_count_values(array_column($logData, 'level'));

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Log Entries', number_format($totalEntries)],
                ['Unique Users', number_format($uniqueUsers)],
                ['Unique Subscriptions', number_format($uniqueSubscriptions)],
                ['INFO Entries', number_format($levelCounts['INFO'] ?? 0)],
                ['WARNING Entries', number_format($levelCounts['WARNING'] ?? 0)],
                ['ERROR Entries', number_format($levelCounts['ERROR'] ?? 0)],
                ['DEBUG Entries', number_format($levelCounts['DEBUG'] ?? 0)],
            ]
        );
        $this->newLine();
    }

    /**
     * Display event breakdown
     */
    private function displayEventBreakdown(array $logData): void
    {
        $this->info('🎯 Event Breakdown');
        $this->line('─────────────────');

        $eventCounts = array_count_values(array_column($logData, 'event'));
        arsort($eventCounts);

        $tableData = [];
        foreach ($eventCounts as $event => $count) {
            $percentage = round(($count / count($logData)) * 100, 2);
            $tableData[] = [$event, number_format($count), "{$percentage}%"];
        }

        $this->table(['Event Type', 'Count', 'Percentage'], $tableData);
        $this->newLine();
    }

    /**
     * Display error analysis
     */
    private function displayErrorAnalysis(array $logData): void
    {
        $errors = array_filter($logData, fn($entry) => $entry['level'] === 'ERROR');
        
        if (empty($errors)) {
            $this->info('✅ No errors found in the analyzed period.');
            $this->newLine();
            return;
        }

        $this->error('🚨 Error Analysis');
        $this->line('─────────────────');

        $errorMessages = array_column($errors, 'message');
        $errorCounts = array_count_values($errorMessages);
        arsort($errorCounts);

        $tableData = [];
        foreach (array_slice($errorCounts, 0, 10) as $message => $count) {
            $tableData[] = [
                substr($message, 0, 60) . (strlen($message) > 60 ? '...' : ''),
                number_format($count)
            ];
        }

        $this->table(['Error Message', 'Count'], $tableData);
        $this->newLine();
    }

    /**
     * Display status code analysis
     */
    private function displayStatusCodeAnalysis(array $logData): void
    {
        $statusEntries = array_filter($logData, fn($entry) => $entry['event'] === 'status_interpretation');
        
        if (empty($statusEntries)) {
            $this->info('ℹ️  No status code interpretations found.');
            $this->newLine();
            return;
        }

        $this->info('📈 Status Code Analysis');
        $this->line('─────────────────────');

        $statusCodes = [];
        foreach ($statusEntries as $entry) {
            $rawStatus = $entry['context']['raw_status'] ?? 'unknown';
            $statusCodes[] = $rawStatus;
        }

        $statusCounts = array_count_values($statusCodes);
        arsort($statusCounts);

        $tableData = [];
        foreach ($statusCounts as $status => $count) {
            $tableData[] = [$status, number_format($count)];
        }

        $this->table(['Status Code', 'Count'], $tableData);
        $this->newLine();
    }

    /**
     * Display performance metrics
     */
    private function displayPerformanceMetrics(array $logData): void
    {
        $this->info('⚡ Performance Metrics');
        $this->line('─────────────────────');

        $upgrades = $this->getUpgradeFlows($logData);
        
        if (empty($upgrades)) {
            $this->info('No complete upgrade flows found.');
            $this->newLine();
            return;
        }

        $successfulUpgrades = array_filter($upgrades, fn($upgrade) => $upgrade['outcome'] === 'success');
        $failedUpgrades = array_filter($upgrades, fn($upgrade) => $upgrade['outcome'] === 'failed');

        $successRate = count($upgrades) > 0 ? round((count($successfulUpgrades) / count($upgrades)) * 100, 2) : 0;

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Upgrade Attempts', number_format(count($upgrades))],
                ['Successful Upgrades', number_format(count($successfulUpgrades))],
                ['Failed Upgrades', number_format(count($failedUpgrades))],
                ['Success Rate', "{$successRate}%"],
            ]
        );
        $this->newLine();
    }

    /**
     * Get upgrade flows from log data
     */
    private function getUpgradeFlows(array $logData): array
    {
        $flows = [];
        $initiatedUpgrades = array_filter($logData, fn($entry) => $entry['event'] === 'upgrade_initiated');

        foreach ($initiatedUpgrades as $initiated) {
            $subscriptionId = $initiated['subscription_id'];
            if (!$subscriptionId) continue;

            $completed = array_filter($logData, fn($entry) => 
                $entry['event'] === 'upgrade_completed' && 
                $entry['subscription_id'] === $subscriptionId
            );

            $rollback = array_filter($logData, fn($entry) => 
                $entry['event'] === 'upgrade_rollback' && 
                $entry['subscription_id'] === $subscriptionId
            );

            $outcome = 'pending';
            if (!empty($completed)) {
                $outcome = 'success';
            } elseif (!empty($rollback)) {
                $outcome = 'failed';
            }

            $flows[] = [
                'subscription_id' => $subscriptionId,
                'initiated_at' => $initiated['timestamp'],
                'outcome' => $outcome
            ];
        }

        return $flows;
    }

    /**
     * Output data as JSON
     */
    private function outputJson(array $logData): void
    {
        $this->info('📄 JSON Output');
        $this->line('─────────────');
        echo json_encode($logData, JSON_PRETTY_PRINT);
        $this->newLine();
    }

    /**
     * Output data as CSV
     */
    private function outputCsv(array $logData): void
    {
        $filename = storage_path('logs/subscription-upgrade-analysis-' . date('Y-m-d-H-i-s') . '.csv');
        
        $file = fopen($filename, 'w');
        fputcsv($file, ['timestamp', 'level', 'event', 'user_id', 'subscription_id', 'message']);

        foreach ($logData as $entry) {
            fputcsv($file, [
                $entry['timestamp'],
                $entry['level'],
                $entry['event'],
                $entry['user_id'],
                $entry['subscription_id'],
                $entry['message']
            ]);
        }

        fclose($file);
        $this->info("📊 CSV exported to: {$filename}");
        $this->newLine();
    }
}
