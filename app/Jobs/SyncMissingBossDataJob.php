<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Company;
use App\Models\UserDetail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SyncMissingBossDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $domain;
    protected $penggunaidboss;

    /**
     * Create a new job instance.
     *
     * @param mixed $domain
     * @param mixed $penggunaidboss
     */
    public function __construct($domain, $penggunaidboss)
    {
        $this->domain = $domain;
        $this->penggunaidboss = $penggunaidboss;
        // Assign the job to a specific queue if desired.
        $this->onQueue('syncMissingBossData');
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        try {
            Log::info('Starting SyncMissingBossDataJob for penggunaidboss: ' . $this->penggunaidboss);

            $urlPos = config('bizappos.bizappos_api_url');
            // Modified API call: Use the boss identifier ($this->penggunaidboss) instead of the typical username
            $apiResponse = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                'DOMAIN'   => $this->domain,
                'username' => $this->penggunaidboss,  
                'password' => config('bizappos.bizapp_mpw'),
                'platform' => 'POS'
            ])->throw()->json();

            // Validate response and ensure boss data is active
            if (!empty($apiResponse) && isset($apiResponse[0]['STATUS']) && $apiResponse[0]['STATUS'] != "0") {
                // Look up the boss user using the unique pid from the API response
                $bossUser = User::where('pid', $apiResponse[0]['pid'])->first();

                if (!$bossUser) {
                    Log::info('Registering missing boss user');
                    $bossUser = User::create([
                        'username'     => $apiResponse[0]['penggunaid'],
                        'password'     => config('bizappos.bizapp_mpw'),
                        'email'        => $apiResponse[0]['emel'],
                        'isBizappUser' => 'Y',
                        'domain'       => $this->domain,
                        'pid'          => $apiResponse[0]['pid']
                    ]);
                    $bossUser->save();

                    $bossUserDetail = UserDetail::updateOrCreate(
                        ['user_id' => $bossUser->id],
                        [
                            'first_name'      => $apiResponse[0]['nama'],
                            'mobile'          => $apiResponse[0]['nohp'],
                            'avatar'          => $apiResponse[0]['attachmentphoto'],
                            'address'         => $apiResponse[0]['alamat1'] . ', ' . $apiResponse[0]['alamat2'] . ', ' . $apiResponse[0]['alamat3'],
                            'currency'        => $apiResponse[0]['currency'],
                            'bizapp_secretkey' => $apiResponse[0]['secretkey'],
                            'state'           => $apiResponse[0]['negeri'],
                            'postcode'        => $apiResponse[0]['poskod'],
                            'country'         => $apiResponse[0]['country']
                        ]
                    );
                    $bossUserDetail->save();

                    $company = Company::create([
                        'user_id' => $bossUser->id,
                        'com_name' => $apiResponse[0]['namaboss'],
                        'com_address' => $apiResponse[0]['alamat1boss'] . ', ' . $apiResponse[0]['alamat2boss'] . ', ' . $apiResponse[0]['alamat3boss'],
                        'com_state' => $apiResponse[0]['negeriboss'],
                        'com_postcode' => $apiResponse[0]['poskodboss'],
                        'account_type' => 'company',
                        'com_mobile' => $apiResponse[0]['nohpboss'],
                        'com_email' => $apiResponse[0]['emelboss'],
                        'com_country' => $apiResponse[0]['country']
                    ]);
                    $company->save();
                } else {
                    Log::info('Updating existing missing boss user');
                    $bossUser->username = $apiResponse[0]['penggunaid'];
                    $bossUser->password = config('bizappos.bizapp_mpw');
                    $bossUser->save();

                    $bossUserDetail = $bossUser->userDetails;
                    if ($bossUserDetail) {
                        $bossUserDetail->first_name = $apiResponse[0]['nama'];
                        $bossUserDetail->currency = $apiResponse[0]['currency'];
                        $bossUserDetail->bizapp_secretkey = $apiResponse[0]['secretkey'];
                        $bossUserDetail->save();
                    }
                }

                Log::info('SyncMissingBossDataJob completed successfully for penggunaidboss: ' . $this->penggunaidboss);
            } else {
                Log::info('No valid boss data found or boss is inactive for penggunaidboss: ' . $this->penggunaidboss);
            }
        } catch (\Exception $e) {
            Log::error('Error in SyncMissingBossDataJob: ' . $e->getMessage(), ['exception' => $e]);
            throw $e;
        }
    }
} 