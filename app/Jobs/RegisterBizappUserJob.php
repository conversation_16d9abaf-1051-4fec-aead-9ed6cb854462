<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Company;
use App\Models\Employee;
use App\Models\UserDetail;
use App\Models\CompanyBranch;
use Illuminate\Bus\Queueable;
use App\Jobs\SyncOrdersByPIDJob;
use App\Jobs\SyncProductBizappJob;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Jobs\SyncMissingBossDataJob;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class RegisterBizappUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $domain;
    protected $username;
    protected $password;
    protected $user;
    /**
     * Create a new job instance.
     */
    public function __construct($domain,$username,$password)
    {
        $this->domain = $domain;
        $this->username = $username;
        $this->password = $password;
        $this->onQueue('registerUserBizapp');
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        try {
                Log::info('Preparing form data {register bizapp user}');
               // login with bizapp credentials
               $urlPos = config('bizappos.bizappos_api_url');
               $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                   'DOMAIN' => $this->domain,
                   'username' => $this->username,
                   'password' => $this->password,
                   'platform' => 'POS'
               ])->throw()->json();


               // check for login status
               // user mush be active in bizapp to be registered STATUS = 1
               // user must be a HQ to be register company info and products
               if (!empty($bizappLogin) && isset($bizappLogin[0]['STATUS']) && $bizappLogin[0]['STATUS'] != "0") {
                   $findUserLocal = User::where('pid',$bizappLogin[0]['pid'])->where('domain',$this->domain)->first();
                   // if user not in db yet register for them
                   Log::info('Find user while registering : ' . $findUserLocal);

                   if(!$findUserLocal){
                    Log::info('Registering new user ');
                       $user = User::create([
                           'username' => $bizappLogin[0]['penggunaid'],
                           'password' => $this->password,
                           'email' => $bizappLogin[0]['emel'],
                           'isBizappUser' => 'Y',
                           'onboarding_complete' => true,
                           'domain' => 'BIZAPP',
                           'pid' => $bizappLogin[0]['pid']
                       ]);
                       $user->save();

                       $userDetail = UserDetail::updateOrCreate(
                            ['user_id' => $user->id],
                            [
                                'first_name' => $bizappLogin[0]['nama'],
                                'mobile' => $bizappLogin[0]['nohp'],
                                'avatar' => $bizappLogin[0]['attachmentphoto'],
                                'address' => $bizappLogin[0]['alamat1'] . ', ' . $bizappLogin[0]['alamat2'] . ', ' . $bizappLogin[0]['alamat3'],
                                'currency' => $bizappLogin[0]['currency'],
                                'bizapp_secretkey' => $bizappLogin[0]['secretkey'],
                                'state' => $bizappLogin[0]['negeri'],
                                'postcode' => $bizappLogin[0]['poskod'],
                                'country' => $bizappLogin[0]['country'],
                                'bizapp_exp_date' => \Carbon\Carbon::createFromFormat('j/n/Y h:i a', $bizappLogin[0]['tarikhtamattempoh'])
                       ]);
                       $userDetail->save();

                       // check if user is a HQ
                       if ($bizappLogin[0]['pid'] == $bizappLogin[0]['pidboss']){
                           $company = Company::create([
                               'user_id' => $user->id,
                               'com_name' => $bizappLogin[0]['namaboss'],
                               'com_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
                               'com_state' => $bizappLogin[0]['negeriboss'],
                               'com_postcode' => $bizappLogin[0]['poskodboss'],
                               'account_type' => 'company',
                               'ic_number' => '',
                               'com_mobile' => $bizappLogin[0]['nohpboss'],
                               'com_email' => $bizappLogin[0]['emelboss'],
                               'com_country' => $bizappLogin[0]['country']
                           ]);
                           $company->save();
                       } else {  
                        // user is staff
                           // save employee data & company branch & access module
                           $boss = User::where('pid',$bizappLogin[0]['pidboss'])->first();

                           if($boss){
                               $employee = Employee::updateOrCreate([
                                   'user_id' => $user->id,
                                   'boss_id' => $boss->id,
                                   'company_id' => $boss->companies->id, // attempt to get ID on null error
                                   'emp_jobTitle' => 'staff',
                               ]);
                               $employee->save();
                                $company = Company::where('user_id',$boss->id)->first();

                                // if($company){
                                //     $companyBranch = CompanyBranch::updateOrCreate([
                                //         'branch_of' => $company->id,
                                //         'br_name' => $bizappLogin[0]['nama'],
                                //         'person_in_charge' => $employee->id,
                                //         'br_address' => $bizappLogin[0]['alamat1boss'] . ', ' . $bizappLogin[0]['alamat2boss'] . ', ' . $bizappLogin[0]['alamat3boss'],
                                //         'br_state' => $bizappLogin[0]['negeriboss'],
                                //         'br_postcode' => $bizappLogin[0]['poskodboss'],
                                //         'br_country' => $bizappLogin[0]['country']
                                //         ]);
                                //         $companyBranch->save();
                                // }
                           } else {
                            Log::error('RegisterBizappUserJob : Boss not found for user ' . $user->username);
                            // To ensure subsequent code waits for this job to complete:
                            SyncMissingBossDataJob::dispatchSync($this->domain, $bizappLogin[0]['penggunaidboss']);
                            // dispatchSync() will block until job completes
                            Log::info('RegisterBizappUserJob : Boss registration job dispatched for user ' . $user->username);
                            // After sync job completes, refetch boss with company relationship
                            $boss2 = User::with('companies')->where('pid', $bizappLogin[0]['pidboss'])->firstOrFail();

                            // Ensure company exists before accessing relationship
                            $companyId = $boss2->companies()->firstOrFail()->id;

                            $employee2 = Employee::updateOrCreate([
                                'user_id' => $user->id,
                                'boss_id' => $boss2->id,
                                'company_id' => $companyId,
                                'emp_jobTitle' => 'staff',
                            ]);
                            $employee2->save();
                           }
                       }
                   } else {
                        Log::info('update registered user : ');
                        $user = $findUserLocal;
                        $user->username = $bizappLogin[0]['penggunaid'];
                        $user->password = $this->password;
                        $user->save();

                       $userDetails = $findUserLocal->userDetails;
                       $userDetails->first_name = $bizappLogin[0]['nama'];
                       $userDetails->currency = $bizappLogin[0]['currency'];
                       $userDetails->bizapp_secretkey = $bizappLogin[0]['secretkey'];
                       $userDetails->bizapp_exp_date = \Carbon\Carbon::createFromFormat('j/n/Y h:i a', $bizappLogin[0]['tarikhtamattempoh']);
                       $userDetails->save();
                   }
               Log::info('Registered bizapp user successfully');

               // Store the user in the job's properties so it's available to the next job
               $this->user = $user;

               Log::info('Dispatching sync jobs for user ID: ' . $user->id);
               // Now chain the sync jobs.


                SyncOrdersByPIDJob::dispatch($user,0)->onQueue('syncOrdersByPID');
                SyncProductBizappJob::dispatch($user)->onQueue('syncProduct');


               Log::info('Job chain dispatched successfully');

               return $user; // Return the user for the next job in chain
               } else {
                Log::info('User not registered');
               }
        } catch (\Exception $e) {
            Log::error('Error in registerBizappUserJob : ' . $e->getMessage(), ['exception' => $e]);
            throw $e;
        }
    }
}
