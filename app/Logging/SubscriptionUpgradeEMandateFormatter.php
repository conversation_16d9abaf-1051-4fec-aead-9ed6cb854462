<?php

namespace App\Logging;

use Monolog\Logger;
use Monolog\Formatter\LineFormatter;
use Monolog\Processor\IntrospectionProcessor;
use Monolog\Processor\WebProcessor;
use Monolog\Processor\MemoryUsageProcessor;

class SubscriptionUpgradeEMandateFormatter
{
    /**
     * Customize the given logger instance.
     */
    public function __invoke(Logger $logger): void
    {
        // Add processors for additional context
        $logger->pushProcessor(new IntrospectionProcessor());
        $logger->pushProcessor(new WebProcessor());
        $logger->pushProcessor(new MemoryUsageProcessor());
        
        // Add custom processor for subscription upgrade context
        $logger->pushProcessor([$this, 'addSubscriptionContext']);

        // Set custom formatter
        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new LineFormatter(
                "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
                'Y-m-d H:i:s',
                true,
                true
            ));
        }
    }

    /**
     * Add subscription upgrade specific context to log records
     */
    public function addSubscriptionContext(array $record): array
    {
        // Add request ID for tracking across multiple log entries
        if (!isset($record['extra']['request_id'])) {
            $record['extra']['request_id'] = request()->header('X-Request-ID') ?? uniqid('req_', true);
        }

        // Add user context if available
        if (auth()->check()) {
            $record['extra']['user_id'] = auth()->id();
            $record['extra']['user_email'] = auth()->user()->email;
            
            if (auth()->user()->companies) {
                $record['extra']['company_id'] = auth()->user()->companies->id;
            }
        }

        // Add timestamp in multiple formats for easier parsing
        $record['extra']['timestamp_unix'] = time();
        $record['extra']['timestamp_iso'] = now()->toISOString();

        return $record;
    }
}
