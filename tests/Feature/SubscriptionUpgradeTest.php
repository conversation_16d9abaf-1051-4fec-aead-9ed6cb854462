<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Company;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Services\SubscriptionEMandateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class SubscriptionUpgradeTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $company;
    protected $currentPlan;
    protected $upgradePlan;
    protected $currentSubscription;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and company
        $this->user = User::factory()->create();
        $this->company = Company::factory()->create(['user_id' => $this->user->id]);

        // Create subscription plans
        $this->currentPlan = SubscriptionPlan::factory()->create([
            'name' => 'Basic Plan',
            'price' => 50.00,
            'billing_frequency' => 'monthly',
            'duration_in_seconds' => 30 * 24 * 60 * 60, // 30 days
        ]);

        $this->upgradePlan = SubscriptionPlan::factory()->create([
            'name' => 'Premium Plan',
            'price' => 100.00,
            'billing_frequency' => 'monthly',
            'duration_in_seconds' => 30 * 24 * 60 * 60, // 30 days
        ]);

        // Create current subscription
        $this->currentSubscription = Subscription::factory()->create([
            'company_id' => $this->company->id,
            'subscription_plan_id' => $this->currentPlan->id,
            'starts_at' => now()->subDays(15), // Started 15 days ago
            'ends_at' => now()->addDays(15), // Ends in 15 days
            'status' => 'active'
        ]);
    }

    /** @test */
    public function user_can_view_upgrade_form()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('user.subscriptions.upgrade.form', $this->upgradePlan->id));

        $response->assertStatus(200);
        $response->assertViewIs('user.subscriptions.upgrade');
        $response->assertViewHas('plan', $this->upgradePlan);
        $response->assertViewHas('currentSubscription', $this->currentSubscription);
    }

    /** @test */
    public function user_cannot_upgrade_to_cheaper_plan()
    {
        $this->actingAs($this->user);

        $cheaperPlan = SubscriptionPlan::factory()->create([
            'name' => 'Cheaper Plan',
            'price' => 25.00,
            'billing_frequency' => 'monthly',
        ]);

        $response = $this->get(route('user.subscriptions.upgrade.form', $cheaperPlan->id));

        $response->assertRedirect(route('user.subscriptions.index'));
        $response->assertSessionHas('error');
    }

    /** @test */
    public function upgrade_process_creates_new_subscription_with_correct_fields()
    {
        $this->actingAs($this->user);

        // Mock the SubscriptionEMandateService
        $this->mock(SubscriptionEMandateService::class, function ($mock) {
            $mock->shouldReceive('createSubscriptionEMandate')
                ->once()
                ->andReturn([
                    'success' => true,
                    'is_existing' => false,
                    'redirect_url' => 'https://bayarcash.test/enrollment/123',
                    'enrollment' => (object) ['id' => 'test-enrollment-id']
                ]);
        });

        $response = $this->post(route('user.subscriptions.upgrade.process', $this->upgradePlan->id));

        // Should redirect to BayarCash enrollment
        $response->assertRedirect('https://bayarcash.test/enrollment/123');

        // Check that new subscription was created
        $newSubscription = Subscription::where('company_id', $this->company->id)
            ->where('subscription_plan_id', $this->upgradePlan->id)
            ->first();

        $this->assertNotNull($newSubscription);
        $this->assertTrue($newSubscription->is_upgrade);
        $this->assertEquals($this->currentSubscription->id, $newSubscription->previous_subscription_id);
        $this->assertEquals('pending', $newSubscription->status);
        $this->assertGreaterThan(0, $newSubscription->upgrade_amount);

        // Check that current subscription was marked for cancellation
        $this->currentSubscription->refresh();
        $this->assertNotNull($this->currentSubscription->canceled_at);
    }

    /** @test */
    public function upgrade_amount_calculation_is_correct()
    {
        $this->actingAs($this->user);

        // Create a reflection of the controller to test private method
        $controller = new \App\Http\Controllers\User\SubscriptionManagementController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('calculateUpgradeAmount');
        $method->setAccessible(true);

        $upgradeAmount = $method->invoke($controller, $this->currentSubscription, $this->upgradePlan);

        // Expected: (100 - 50) * (15 remaining days / 30 total days) = 50 * 0.5 = 25
        $expectedAmount = 25.00;
        $this->assertEquals($expectedAmount, $upgradeAmount);
    }

    /** @test */
    public function upgrade_fails_gracefully_when_emandate_service_fails()
    {
        $this->actingAs($this->user);

        // Mock the SubscriptionEMandateService to fail
        $this->mock(SubscriptionEMandateService::class, function ($mock) {
            $mock->shouldReceive('createSubscriptionEMandate')
                ->once()
                ->andReturn([
                    'success' => false,
                    'message' => 'E-mandate enrollment failed'
                ]);
        });

        $response = $this->post(route('user.subscriptions.upgrade.process', $this->upgradePlan->id));

        $response->assertRedirect(route('user.subscriptions.management'));
        $response->assertSessionHas('error');

        // Check that no new subscription was created
        $newSubscription = Subscription::where('company_id', $this->company->id)
            ->where('subscription_plan_id', $this->upgradePlan->id)
            ->first();

        $this->assertNull($newSubscription);

        // Check that current subscription was not modified
        $this->currentSubscription->refresh();
        $this->assertNull($this->currentSubscription->canceled_at);
    }
}
