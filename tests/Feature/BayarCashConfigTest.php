<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\CustomBayarCashSDK;
use App\Services\BayarCashEMandateService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BayarCashConfigTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test CustomBayarCashSDK handles null token gracefully
     */
    public function test_custom_bayarcash_sdk_handles_null_token(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('BayarCash API token is required but not provided');

        new CustomBayarCashSDK(null);
    }

    /**
     * Test CustomBayarCashSDK handles empty token gracefully
     */
    public function test_custom_bayarcash_sdk_handles_empty_token(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('BayarCash API token is required but not provided');

        new CustomBayarCashSDK('');
    }

    /**
     * Test BayarCashEMandateService handles missing configuration
     */
    public function test_bayarcash_emandate_service_handles_missing_config(): void
    {
        // Temporarily clear the config
        config(['services.bayarcash.api_token' => null]);
        config(['services.bayarcash.api_secret_key' => null]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('BayarCash configuration is incomplete');

        new BayarCashEMandateService();
    }

    /**
     * Test middleware blocks requests when configuration is missing
     */
    public function test_middleware_blocks_requests_with_missing_config(): void
    {
        // Temporarily clear the config
        config(['services.bayarcash.api_token' => null]);

        $response = $this->get('/subscription-management/upgrade/test-plan-id');

        $response->assertRedirect();
        $response->assertSessionHasErrors(['payment']);
    }

    /**
     * Test configuration check command
     */
    public function test_configuration_check_command(): void
    {
        $this->artisan('bayarcash:check-config')
            ->assertExitCode(0);
    }
}
