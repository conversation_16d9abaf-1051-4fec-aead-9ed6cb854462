<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Company;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Services\SubscriptionUpgradeLogger;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SubscriptionUpgradeLoggingTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $company;
    protected $currentPlan;
    protected $upgradePlan;
    protected $currentSubscription;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and company
        $this->user = User::factory()->create();
        $this->company = Company::factory()->create(['user_id' => $this->user->id]);

        // Create subscription plans
        $this->currentPlan = SubscriptionPlan::factory()->create([
            'name' => 'Basic Plan',
            'price' => 50.00,
            'billing_frequency' => 'monthly',
        ]);

        $this->upgradePlan = SubscriptionPlan::factory()->create([
            'name' => 'Premium Plan',
            'price' => 100.00,
            'billing_frequency' => 'monthly',
        ]);

        // Create current subscription
        $this->currentSubscription = Subscription::factory()->create([
            'company_id' => $this->company->id,
            'subscription_plan_id' => $this->currentPlan->id,
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_logs_upgrade_initiation()
    {
        // Mock the log channel
        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('=== SUBSCRIPTION UPGRADE INITIATED ===', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logUpgradeInitiation(
            $this->user,
            $this->company,
            $this->currentSubscription,
            $this->upgradePlan,
            25.00
        );

        $this->assertTrue(true); // Test passes if no exceptions thrown
    }

    /** @test */
    public function it_logs_upgrade_amount_calculation()
    {
        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('debug')
            ->once()
            ->with('Upgrade amount calculation performed', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logUpgradeAmountCalculation(
            $this->currentSubscription,
            $this->upgradePlan,
            25.00,
            ['calculation_method' => 'pro_rated']
        );

        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_new_subscription_creation()
    {
        $newSubscription = Subscription::factory()->create([
            'company_id' => $this->company->id,
            'subscription_plan_id' => $this->upgradePlan->id,
            'status' => 'pending',
            'is_upgrade' => true,
            'upgrade_amount' => 25.00,
            'previous_subscription_id' => $this->currentSubscription->id
        ]);

        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('New upgrade subscription created', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logNewSubscriptionCreated($newSubscription);

        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_emandate_enrollment_attempt()
    {
        $enrollmentData = [
            'amount' => 25.00,
            'frequency_mode' => 'MT',
            'payer_email' => $this->user->email,
            'application_reason' => 'Subscription upgrade',
            'metadata' => ['subscription_id' => $this->currentSubscription->id]
        ];

        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('E-mandate enrollment attempt started', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logEMandateEnrollmentAttempt(
            $this->currentSubscription,
            $enrollmentData
        );

        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_status_interpretation()
    {
        $statusInterpretation = [
            'status' => 'New',
            'description' => 'E-mandate enrollment is new/pending',
            'is_success' => false,
            'is_failure' => false,
            'is_pending' => true
        ];

        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('warning')
            ->once()
            ->with('BayarCash status code interpretation', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logStatusInterpretation(
            '0',
            $statusInterpretation,
            'ORDER123'
        );

        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_upgrade_completion()
    {
        $newSubscription = Subscription::factory()->create([
            'company_id' => $this->company->id,
            'subscription_plan_id' => $this->upgradePlan->id,
            'status' => 'active',
            'is_upgrade' => true
        ]);

        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('=== SUBSCRIPTION UPGRADE COMPLETED ===', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logUpgradeCompletion(
            $newSubscription,
            'success',
            'Upgrade completed successfully'
        );

        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_upgrade_rollback()
    {
        $newSubscription = Subscription::factory()->create([
            'company_id' => $this->company->id,
            'subscription_plan_id' => $this->upgradePlan->id,
            'status' => 'pending',
            'is_upgrade' => true
        ]);

        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('error')
            ->once()
            ->with('=== SUBSCRIPTION UPGRADE ROLLBACK ===', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logUpgradeRollback(
            $newSubscription,
            $this->currentSubscription,
            'E-mandate enrollment failed'
        );

        $this->assertTrue(true);
    }

    /** @test */
    public function it_logs_errors_with_context()
    {
        $exception = new \Exception('Test error message');

        Log::shouldReceive('channel')
            ->with('subscription_upgrade_emandate')
            ->andReturnSelf();

        Log::shouldReceive('error')
            ->once()
            ->with('Error in test_context', \Mockery::type('array'));

        SubscriptionUpgradeLogger::logError(
            'test_context',
            $exception,
            ['additional_data' => 'test_value']
        );

        $this->assertTrue(true);
    }
}
