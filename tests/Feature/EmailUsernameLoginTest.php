<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\UserDetail;
use App\Models\Company;
use Illuminate\Support\Facades\Hash;

class EmailUsernameLoginTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with both username and email
        $this->testUser = User::create([
            'username' => 'testuser123',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'pid' => 'TEST001',
            'isBizappUser' => 'N',
            'domain' => null,
            'access_module' => null,
            'migration_status' => null
        ]);

        // Create user details
        UserDetail::create([
            'user_id' => $this->testUser->id,
            'first_name' => 'Test',
            'mobile' => '**********',
        ]);

        // Create company for the user
        Company::create([
            'user_id' => $this->testUser->id,
            'com_name' => 'Test Company',
            'com_address' => 'Test Address',
            'account_type' => 'company',
            'com_mobile' => '**********',
            'com_email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function user_can_login_with_username()
    {
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login-process', [
                'username' => 'testuser123',
                'password' => 'password123',
            ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($this->testUser);
    }

    /** @test */
    public function user_can_login_with_email()
    {
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login-process', [
                'username' => '<EMAIL>', // Using email in username field
                'password' => 'password123',
            ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($this->testUser);
    }

    /** @test */
    public function login_fails_with_wrong_password()
    {
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login-process', [
                'username' => 'testuser123',
                'password' => 'wrongpassword',
            ]);

        $response->assertRedirect('/login');
        $response->assertSessionHas('error', 'Incorrect password, please try again');
        $this->assertGuest();
    }

    /** @test */
    public function login_fails_with_nonexistent_username()
    {
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login-process', [
                'username' => 'nonexistentuser',
                'password' => 'password123',
            ]);

        $response->assertRedirect('/login');
        $response->assertSessionHas('error');
        $this->assertGuest();
    }

    /** @test */
    public function login_fails_with_nonexistent_email()
    {
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login-process', [
                'username' => '<EMAIL>',
                'password' => 'password123',
            ]);

        $response->assertRedirect('/login');
        $response->assertSessionHas('error');
        $this->assertGuest();
    }

    /** @test */
    public function api_login_works_with_username()
    {
        $response = $this->postJson('/api/v3/login', [
            'username' => 'testuser123',
            'password' => 'password123',
            'device_info' => 'Test Device',
            'device_brand' => 'Test Brand',
            'device_id' => 'test123',
            'app_version' => '1.0.0',
            'device_os' => 'Test OS',
            'db_version' => '1.0',
            'longitude' => '0.0',
            'latitude' => '0.0'
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'result' => true,
            'message' => 'Login Success...'
        ]);
        $response->assertJsonStructure([
            'result',
            'message',
            'data' => [
                'token',
                'user'
            ]
        ]);
    }

    /** @test */
    public function api_login_works_with_email()
    {
        $response = $this->postJson('/api/v3/login', [
            'username' => '<EMAIL>', // Using email in username field
            'password' => 'password123',
            'device_info' => 'Test Device',
            'device_brand' => 'Test Brand',
            'device_id' => 'test123',
            'app_version' => '1.0.0',
            'device_os' => 'Test OS',
            'db_version' => '1.0',
            'longitude' => '0.0',
            'latitude' => '0.0'
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'result' => true,
            'message' => 'Login Success...'
        ]);
        $response->assertJsonStructure([
            'result',
            'message',
            'data' => [
                'token',
                'user'
            ]
        ]);
    }

    /** @test */
    public function api_login_fails_with_wrong_credentials()
    {
        $response = $this->postJson('/api/v3/login', [
            'username' => 'testuser123',
            'password' => 'wrongpassword',
            'device_info' => 'Test Device',
            'device_brand' => 'Test Brand',
            'device_id' => 'test123',
            'app_version' => '1.0.0',
            'device_os' => 'Test OS',
            'db_version' => '1.0',
            'longitude' => '0.0',
            'latitude' => '0.0'
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'result' => false
        ]);
        // The message could be either "Incorrect Username or Password" or "User not found"
        $this->assertTrue(
            str_contains($response->json('message'), 'Incorrect') ||
            str_contains($response->json('message'), 'User not found')
        );
    }

    /** @test */
    public function email_detection_works_correctly()
    {
        // Test that valid emails are detected as emails
        $this->assertTrue(filter_var('<EMAIL>', FILTER_VALIDATE_EMAIL) !== false);
        $this->assertTrue(filter_var('<EMAIL>', FILTER_VALIDATE_EMAIL) !== false);
        
        // Test that usernames are not detected as emails
        $this->assertFalse(filter_var('username123', FILTER_VALIDATE_EMAIL) !== false);
        $this->assertFalse(filter_var('user@', FILTER_VALIDATE_EMAIL) !== false);
        $this->assertFalse(filter_var('@domain.com', FILTER_VALIDATE_EMAIL) !== false);
    }

    /** @test */
    public function case_insensitive_login_works()
    {
        // Test username case insensitivity
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login-process', [
                'username' => 'TESTUSER123',
                'password' => 'password123',
            ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($this->testUser);

        // Logout for next test
        auth()->logout();

        // Test email case insensitivity
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/login-process', [
                'username' => '<EMAIL>',
                'password' => 'password123',
            ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($this->testUser);
    }
}
