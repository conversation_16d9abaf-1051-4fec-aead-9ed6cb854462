<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Feature;
use Illuminate\Support\Facades\Log;

class FeaturesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define all features for the BizappOS system
        $features = [
            // Core Features (included in all subscription plans)
            [
                'name' => 'Product Management',
                'code' => 'CF-PRODUCT-MANAGEMENT',
                'description' => 'Create, edit, and manage product inventory',
                'type' => 'core',
                'purchase_type' => 'subscription',
                'price' => 0.00,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Basic product management functionality',
                    'icon' => 'fas fa-box'
                ]
            ],
            [
                'name' => 'Order Management',
                'code' => 'CF-ORDER-MANAGEMENT',
                'description' => 'Process and track customer orders',
                'type' => 'core',
                'purchase_type' => 'subscription',
                'price' => 0.00,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Basic order processing and tracking',
                    'icon' => 'fas fa-shopping-cart'
                ]
            ],
            [
                'name' => 'Customer Management',
                'code' => 'CF-CUSTOMER-MANAGEMENT',
                'description' => 'Manage customer information and relationships',
                'type' => 'core',
                'purchase_type' => 'subscription',
                'price' => 0.00,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Customer database and relationship management',
                    'icon' => 'fas fa-users'
                ]
            ],
            [
                'name' => 'Basic Reporting',
                'code' => 'CF-BASIC-REPORTING',
                'description' => 'Generate basic sales and inventory reports',
                'type' => 'core',
                'purchase_type' => 'subscription',
                'price' => 0.00,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Essential reporting functionality',
                    'icon' => 'fas fa-chart-bar'
                ]
            ],
            [
                'name' => 'Receipt Generation',
                'code' => 'CF-RECEIPT-GENERATION',
                'description' => 'Generate and print receipts for transactions',
                'type' => 'core',
                'purchase_type' => 'subscription',
                'price' => 0.00,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Basic receipt printing functionality',
                    'icon' => 'fas fa-receipt'
                ]
            ],

            // Core Limit Features
            [
                'name' => 'Product SKU Limit',
                'code' => 'CF-PRODUCT-SKU-LIMIT',
                'description' => 'Maximum number of product SKUs that can be created',
                'type' => 'core',
                'purchase_type' => 'subscription',
                'price' => 0.00,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Controls the maximum number of product SKUs',
                    'icon' => 'fas fa-hashtag'
                ]
            ],
            [
                'name' => 'Staff Account Limit',
                'code' => 'CF-STAFF-ACCOUNT-LIMIT',
                'description' => 'Maximum number of staff accounts that can be created',
                'type' => 'core',
                'purchase_type' => 'subscription',
                'price' => 0.00,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Controls the maximum number of staff accounts',
                    'icon' => 'fas fa-user-friends'
                ]
            ],

            // Addon Features (AO- prefix for Add-On features)
            [
                'name' => 'Advanced Analytics',
                'code' => 'AO-ADVANCED-ANALYTICS',
                'description' => 'Detailed analytics and business intelligence reports',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 19.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Advanced reporting with charts and insights',
                    'icon' => 'fas fa-chart-line'
                ]
            ],
            [
                'name' => 'Multi-Location Management',
                'code' => 'AO-MULTI-LOCATION',
                'description' => 'Manage multiple store locations and branches',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 29.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Support for multiple business locations',
                    'icon' => 'fas fa-map-marker-alt'
                ]
            ],
            [
                'name' => 'Barcode Scanner Integration',
                'code' => 'AO-BARCODE-SCANNER',
                'description' => 'Integrate with barcode scanners for faster processing',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 15.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Barcode scanning functionality',
                    'icon' => 'fas fa-barcode'
                ]
            ],
            [
                'name' => 'Email Marketing',
                'code' => 'AO-EMAIL-MARKETING',
                'description' => 'Send promotional emails and newsletters to customers',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 24.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Email marketing and customer communication',
                    'icon' => 'fas fa-envelope'
                ]
            ],
            [
                'name' => 'SMS Notifications',
                'code' => 'AO-SMS-NOTIFICATIONS',
                'description' => 'Send SMS notifications to customers and staff',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 12.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'SMS messaging functionality',
                    'icon' => 'fas fa-sms'
                ]
            ],
            [
                'name' => 'Loyalty Program',
                'code' => 'AO-LOYALTY-PROGRAM',
                'description' => 'Customer loyalty points and rewards system',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 34.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Customer loyalty and rewards management',
                    'icon' => 'fas fa-gift'
                ]
            ],
            [
                'name' => 'Advanced Inventory Tracking',
                'code' => 'AO-ADVANCED-INVENTORY',
                'description' => 'Advanced inventory management with low stock alerts',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 22.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Enhanced inventory tracking and alerts',
                    'icon' => 'fas fa-warehouse'
                ]
            ],
            [
                'name' => 'API Access',
                'code' => 'AO-API-ACCESS',
                'description' => 'Access to REST API for third-party integrations',
                'type' => 'addon',
                'purchase_type' => 'subscription',
                'price' => 39.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'API access for custom integrations',
                    'icon' => 'fas fa-code'
                ]
            ],
            [
                'name' => 'Custom Branding',
                'code' => 'AO-CUSTOM-BRANDING',
                'description' => 'Customize receipts and interface with your brand',
                'type' => 'addon',
                'purchase_type' => 'one_time',
                'price' => 99.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Brand customization options',
                    'icon' => 'fas fa-palette'
                ]
            ],
        ];

        foreach ($features as $featureData) {
            // Check if feature already exists
            $existingFeature = Feature::where('code', $featureData['code'])->first();

            if ($existingFeature) {
                Log::info("Feature '{$featureData['code']}' already exists, skipping creation");
                continue;
            }

            // Create the feature
            $feature = Feature::create($featureData);

            Log::info("Created feature: {$feature->name}", [
                'feature_id' => $feature->id,
                'code' => $feature->code,
                'type' => $feature->type
            ]);
        }

        // Create limit upgrade features
        $this->createLimitUpgradeFeatures();
    }

    /**
     * Create limit upgrade features
     */
    private function createLimitUpgradeFeatures(): void
    {
        $limitUpgradeFeatures = [
            // Product SKU Limit Upgrades
            [
                'name' => 'Unlimited Product SKUs',
                'code' => 'LU-UNLIMITED-PRODUCT-SKU',
                'description' => 'Remove the limit on the number of product SKUs you can create',
                'type' => 'limit_upgrade',
                'purchase_type' => 'one_time',
                'price' => 49.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Upgrade to unlimited product SKUs',
                    'base_feature' => 'CF-PRODUCT-SKU-LIMIT',
                    'upgrade_type' => 'unlimited',
                    'icon' => 'fas fa-infinity'
                ]
            ],
            [
                'name' => '500 Additional Product SKUs',
                'code' => 'LU-ADDITIONAL-500-PRODUCT-SKU',
                'description' => 'Add 500 more product SKUs to your current limit',
                'type' => 'limit_upgrade',
                'purchase_type' => 'one_time',
                'price' => 19.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Add 500 more product SKUs',
                    'base_feature' => 'CF-PRODUCT-SKU-LIMIT',
                    'upgrade_type' => 'additional',
                    'upgrade_value' => 500,
                    'icon' => 'fas fa-plus-circle'
                ]
            ],
            [
                'name' => '1000 Additional Product SKUs',
                'code' => 'LU-ADDITIONAL-1000-PRODUCT-SKU',
                'description' => 'Add 1000 more product SKUs to your current limit',
                'type' => 'limit_upgrade',
                'purchase_type' => 'one_time',
                'price' => 34.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Add 1000 more product SKUs',
                    'base_feature' => 'CF-PRODUCT-SKU-LIMIT',
                    'upgrade_type' => 'additional',
                    'upgrade_value' => 1000,
                    'icon' => 'fas fa-plus-circle'
                ]
            ],

            // Staff Account Limit Upgrades
            [
                'name' => 'Unlimited Staff Accounts',
                'code' => 'LU-UNLIMITED-STAFF-ACCOUNT',
                'description' => 'Remove the limit on the number of staff accounts you can create',
                'type' => 'limit_upgrade',
                'purchase_type' => 'one_time',
                'price' => 29.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Upgrade to unlimited staff accounts',
                    'base_feature' => 'CF-STAFF-ACCOUNT-LIMIT',
                    'upgrade_type' => 'unlimited',
                    'icon' => 'fas fa-infinity'
                ]
            ],
            [
                'name' => '5 Additional Staff Accounts',
                'code' => 'LU-ADDITIONAL-5-STAFF-ACCOUNT',
                'description' => 'Add 5 more staff accounts to your current limit',
                'type' => 'limit_upgrade',
                'purchase_type' => 'one_time',
                'price' => 9.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Add 5 more staff accounts',
                    'base_feature' => 'CF-STAFF-ACCOUNT-LIMIT',
                    'upgrade_type' => 'additional',
                    'upgrade_value' => 5,
                    'icon' => 'fas fa-user-plus'
                ]
            ],
            [
                'name' => '10 Additional Staff Accounts',
                'code' => 'LU-ADDITIONAL-10-STAFF-ACCOUNT',
                'description' => 'Add 10 more staff accounts to your current limit',
                'type' => 'limit_upgrade',
                'purchase_type' => 'one_time',
                'price' => 17.99,
                'is_active' => true,
                'metadata' => [
                    'description' => 'Add 10 more staff accounts',
                    'base_feature' => 'CF-STAFF-ACCOUNT-LIMIT',
                    'upgrade_type' => 'additional',
                    'upgrade_value' => 10,
                    'icon' => 'fas fa-user-plus'
                ]
            ],
        ];

        foreach ($limitUpgradeFeatures as $featureData) {
            // Check if feature already exists
            $existingFeature = Feature::where('code', $featureData['code'])->first();

            if ($existingFeature) {
                Log::info("Limit upgrade feature '{$featureData['code']}' already exists, skipping creation");
                continue;
            }

            // Create the feature
            $feature = Feature::create($featureData);

            Log::info("Created limit upgrade feature: {$feature->name}", [
                'feature_id' => $feature->id,
                'code' => $feature->code,
                'base_feature' => $feature->metadata['base_feature'] ?? null,
                'upgrade_type' => $feature->metadata['upgrade_type'] ?? null
            ]);
        }
    }
}
