<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Feature;
use Illuminate\Support\Facades\Log;

class SubscriptionPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define subscription plans (excluding "Starter - Trial" which already exists)
        $plans = [
            // Starter Plans
            [
                'name' => 'Starter - Monthly',
                'description' => 'Perfect for small businesses getting started with basic features',
                'price' => 29.00,
                'duration_in_seconds' => 2592000, // 30 days in seconds
                'tier' => 'starter',
                'billing_frequency' => 'monthly',
                'trial_period_days' => 7,
                'is_active' => true,
                'features' => [
                    'CF-PRODUCT-SKU-LIMIT' => ['limit' => 100],
                    'CF-STAFF-ACCOUNT-LIMIT' => ['limit' => 2],
                ]
            ],
            [
                'name' => 'Starter - Yearly',
                'description' => 'Perfect for small businesses getting started with basic features (Annual billing)',
                'price' => 290.00, // 10 months price for 12 months
                'duration_in_seconds' => ********, // 365 days in seconds
                'tier' => 'starter',
                'billing_frequency' => 'yearly',
                'trial_period_days' => 7,
                'is_active' => true,
                'features' => [
                    'CF-PRODUCT-SKU-LIMIT' => ['limit' => 100],
                    'CF-STAFF-ACCOUNT-LIMIT' => ['limit' => 2],
                ]
            ],

            // Plus Plans
            [
                'name' => 'Plus - Monthly',
                'description' => 'Ideal for growing businesses with enhanced features and higher limits',
                'price' => 59.00,
                'duration_in_seconds' => 2592000, // 30 days in seconds
                'tier' => 'plus',
                'billing_frequency' => 'monthly',
                'trial_period_days' => 14,
                'is_active' => true,
                'features' => [
                    'CF-PRODUCT-SKU-LIMIT' => ['limit' => 500],
                    'CF-STAFF-ACCOUNT-LIMIT' => ['limit' => 5],
                ]
            ],
            [
                'name' => 'Plus - Yearly',
                'description' => 'Ideal for growing businesses with enhanced features and higher limits (Annual billing)',
                'price' => 590.00, // 10 months price for 12 months
                'duration_in_seconds' => ********, // 365 days in seconds
                'tier' => 'plus',
                'billing_frequency' => 'yearly',
                'trial_period_days' => 14,
                'is_active' => true,
                'features' => [
                    'CF-PRODUCT-SKU-LIMIT' => ['limit' => 500],
                    'CF-STAFF-ACCOUNT-LIMIT' => ['limit' => 5],
                ]
            ],

            // Advance Plans
            [
                'name' => 'Advance - Monthly',
                'description' => 'For established businesses requiring advanced features and maximum limits',
                'price' => 99.00,
                'duration_in_seconds' => 2592000, // 30 days in seconds
                'tier' => 'advance',
                'billing_frequency' => 'monthly',
                'trial_period_days' => 14,
                'is_active' => true,
                'features' => [
                    'CF-PRODUCT-SKU-LIMIT' => ['limit' => 1000],
                    'CF-STAFF-ACCOUNT-LIMIT' => ['limit' => 10],
                ]
            ],
            [
                'name' => 'Advance - Yearly',
                'description' => 'For established businesses requiring advanced features and maximum limits (Annual billing)',
                'price' => 990.00, // 10 months price for 12 months
                'duration_in_seconds' => ********, // 365 days in seconds
                'tier' => 'advance',
                'billing_frequency' => 'yearly',
                'trial_period_days' => 14,
                'is_active' => true,
                'features' => [
                    'CF-PRODUCT-SKU-LIMIT' => ['limit' => 1000],
                    'CF-STAFF-ACCOUNT-LIMIT' => ['limit' => 10],
                ]
            ],
        ];

        foreach ($plans as $planData) {
            // Check if plan already exists
            $existingPlan = SubscriptionPlan::where('name', $planData['name'])->first();

            if ($existingPlan) {
                Log::info("Subscription plan '{$planData['name']}' already exists, skipping creation");
                continue;
            }

            // Extract features data before creating the plan
            $featuresData = $planData['features'];
            unset($planData['features']);

            // Create the subscription plan
            $plan = SubscriptionPlan::create($planData);

            Log::info("Created subscription plan: {$plan->name}", ['plan_id' => $plan->id]);

            // Attach features to the plan
            $this->attachFeaturesToPlan($plan, $featuresData);
        }
    }

    /**
     * Attach features to a subscription plan with their settings
     */
    private function attachFeaturesToPlan(SubscriptionPlan $plan, array $featuresData)
    {
        // First, attach all core features
        $coreFeatures = Feature::where('type', 'core')
            ->where('is_active', true)
            ->get();

        foreach ($coreFeatures as $feature) {
            $plan->features()->attach($feature->id);
        }

        Log::info("Attached {$coreFeatures->count()} core features to plan: {$plan->name}");

        // Then attach specific limit features with their settings
        foreach ($featuresData as $featureCode => $settings) {
            $feature = Feature::where('code', $featureCode)->first();

            if ($feature) {
                $plan->features()->attach($feature->id, [
                    'settings' => json_encode($settings)
                ]);

                Log::info("Attached feature '{$featureCode}' to plan '{$plan->name}' with settings", [
                    'plan_id' => $plan->id,
                    'feature_id' => $feature->id,
                    'settings' => $settings
                ]);
            } else {
                Log::warning("Feature '{$featureCode}' not found for plan '{$plan->name}'");
            }
        }
    }
}
