<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->decimal('upgrade_amount', 10, 2)->nullable()->after('status');
            $table->boolean('is_upgrade')->default(false)->after('upgrade_amount');
            $table->uuid('previous_subscription_id')->nullable()->after('is_upgrade');
            
            // Add foreign key constraint for previous subscription
            $table->foreign('previous_subscription_id')->references('id')->on('subscriptions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropForeign(['previous_subscription_id']);
            $table->dropColumn(['upgrade_amount', 'is_upgrade', 'previous_subscription_id']);
        });
    }
};
