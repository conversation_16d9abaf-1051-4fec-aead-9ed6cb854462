@extends('backend.layout.default')

@section('title', 'Complete Your Profile for Direct Debit')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        Complete Your Profile for Direct Debit Setup
                    </h4>
                    <p class="mb-0 mt-2">Please provide the required information to proceed with e-mandate enrollment</p>
                </div>
                
                <div class="card-body">
                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if(session('warning'))
                        <div class="alert alert-warning">
                            {{ session('warning') }}
                            @if(session('validation_messages'))
                                <ul class="mb-0 mt-2">
                                    @foreach(session('validation_messages') as $message)
                                        <li>{{ $message }}</li>
                                    @endforeach
                                </ul>
                            @endif
                        </div>
                    @endif

                    @if(!empty($messages))
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Required Information</h6>
                            <ul class="mb-0">
                                @foreach($messages as $message)
                                    <li>{{ $message }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Data Quality Score -->
                    @if(isset($validation['data_quality_score']))
                        <div class="alert alert-{{ $validation['data_quality_score'] >= 80 ? 'success' : ($validation['data_quality_score'] >= 60 ? 'warning' : 'danger') }}">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-chart-line me-2"></i>Profile Completeness</span>
                                <span class="badge bg-{{ $validation['data_quality_score'] >= 80 ? 'success' : ($validation['data_quality_score'] >= 60 ? 'warning' : 'danger') }}">
                                    {{ $validation['data_quality_score'] }}%
                                </span>
                            </div>
                            <div class="progress mt-2" style="height: 8px;">
                                <div class="progress-bar bg-{{ $validation['data_quality_score'] >= 80 ? 'success' : ($validation['data_quality_score'] >= 60 ? 'warning' : 'danger') }}" 
                                     style="width: {{ $validation['data_quality_score'] }}%"></div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('user.emandate.update-data') }}" method="POST" id="emandateDataForm">
                        @csrf
                        
                        <!-- Hidden fields for return URL and subscription ID -->
                        <input type="hidden" name="return_url" value="{{ $returnUrl }}">
                        @if($subscriptionId)
                            <input type="hidden" name="subscription_id" value="{{ $subscriptionId }}">
                        @endif

                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-user me-2"></i>Personal Information
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="{{ old('first_name', $userDetail->first_name ?? '') }}" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="{{ old('last_name', $userDetail->last_name ?? '') }}" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="mobile" class="form-label">Mobile Phone <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="mobile" name="mobile" 
                                           value="{{ old('mobile', $userDetail->mobile ?? '') }}" required
                                           placeholder="e.g., 012-3456789">
                                    <div class="invalid-feedback"></div>
                                    <small class="form-text text-muted">Malaysian mobile number format</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="ic_number" class="form-label">IC Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="ic_number" name="ic_number" 
                                           value="{{ old('ic_number', $company->ic_number ?? '') }}" required
                                           placeholder="e.g., 901234567890">
                                    <div class="invalid-feedback"></div>
                                    <small class="form-text text-muted">12-digit Malaysian IC number</small>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-map-marker-alt me-2"></i>Address Information
                                </h5>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="2" 
                                              placeholder="Street address">{{ old('address', $userDetail->address ?? '') }}</textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="{{ old('city', $userDetail->city ?? '') }}">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control" id="state" name="state" 
                                           value="{{ old('state', $userDetail->state ?? '') }}">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="postcode" class="form-label">Postcode</label>
                                    <input type="text" class="form-control" id="postcode" name="postcode" 
                                           value="{{ old('postcode', $userDetail->postcode ?? '') }}">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="country" class="form-label">Country</label>
                                    <input type="text" class="form-control" id="country" name="country" 
                                           value="{{ old('country', $userDetail->country ?? 'Malaysia') }}" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Company Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-building me-2"></i>Company Information
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="com_name" class="form-label">Company Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="com_name" name="com_name" 
                                           value="{{ old('com_name', $company->com_name ?? '') }}" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="com_mobile" class="form-label">Company Mobile</label>
                                    <input type="text" class="form-control" id="com_mobile" name="com_mobile" 
                                           value="{{ old('com_mobile', $company->com_mobile ?? '') }}"
                                           placeholder="e.g., 03-12345678">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="com_email" class="form-label">Company Email</label>
                                    <input type="email" class="form-control" id="com_email" name="com_email" 
                                           value="{{ old('com_email', $company->com_email ?? '') }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="com_registration_no" class="form-label">Registration Number</label>
                                    <input type="text" class="form-control" id="com_registration_no" name="com_registration_no" 
                                           value="{{ old('com_registration_no', $company->com_registration_no ?? '') }}"
                                           placeholder="Company registration number">
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ $returnUrl }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="fas fa-save me-2"></i>Save & Continue
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('emandateDataForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Real-time validation for specific fields
    const fieldsToValidate = ['mobile', 'ic_number', 'com_mobile', 'com_email'];
    
    fieldsToValidate.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('blur', function() {
                validateField(fieldName, this.value, this);
            });
        }
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    });
    
    function validateField(fieldName, value, element) {
        if (!value.trim()) return; // Skip validation for empty fields
        
        fetch('{{ route("user.emandate.validate-field") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                field: fieldName,
                value: value
            })
        })
        .then(response => response.json())
        .then(data => {
            const feedback = element.parentNode.querySelector('.invalid-feedback');
            if (data.valid) {
                element.classList.remove('is-invalid');
                element.classList.add('is-valid');
                if (feedback) feedback.textContent = '';
            } else {
                element.classList.remove('is-valid');
                element.classList.add('is-invalid');
                if (feedback) feedback.textContent = data.message;
            }
        })
        .catch(error => {
            console.error('Validation error:', error);
        });
    }
});
</script>
@endsection
