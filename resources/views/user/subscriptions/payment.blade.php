@extends('backend.layout.default')

@section('title', 'Payment')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Payment for {{ $subscription->subscriptionPlan->name }}</h3>
                </div>
                <div class="card-body">
                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Order Summary</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <th>Plan:</th>
                                            <td>{{ $subscription->subscriptionPlan->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Tier:</th>
                                            <td>
                                                <span class="badge bg-{{ $subscription->subscriptionPlan->tier === 'basic' ? 'secondary' : ($subscription->subscriptionPlan->tier === 'premium' ? 'primary' : 'success') }}">
                                                    {{ ucfirst($subscription->subscriptionPlan->tier) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Duration:</th>
                                            <td>{{ $subscription->subscriptionPlan->duration_in_days }} days</td>
                                        </tr>
                                        <tr>
                                            <th>Billing Cycle:</th>
                                            <td>{{ ucfirst($subscription->subscriptionPlan->billing_frequency) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Amount:</th>
                                            <td>RM {{ number_format($subscription->subscriptionPlan->price, 2) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Direct Debit Setup</h5>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('user.subscriptions.process-payment', $subscription->id) }}" method="POST">
                                        @csrf
                                        <input type="hidden" name="payment_method" value="bayarcash_emandate">

                                        <div class="alert alert-info">
                                            <i class="fas fa-university mr-2"></i>
                                            <strong>FPX Direct Debit (E-Mandate)</strong>
                                            <p class="mb-0 mt-2">Set up automatic recurring payments through your bank account. This is secure, convenient, and ensures uninterrupted service.</p>
                                        </div>

                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle mr-2"></i>
                                            <strong>Benefits:</strong>
                                            <ul class="mb-0 mt-2">
                                                <li>Automatic recurring payments</li>
                                                <li>No need to remember payment dates</li>
                                                <li>Secure bank-level authentication</li>
                                                <li>Can be cancelled anytime</li>
                                            </ul>
                                        </div>

                                        <div class="form-group mt-4">
                                            <button type="submit" class="btn btn-primary btn-block">
                                                <i class="fas fa-university mr-1"></i> Setup Direct Debit
                                            </button>
                                            <a href="{{ route('user.subscriptions.management') }}" class="btn btn-default btn-block mt-2">
                                                Cancel
                                            </a>
                                        </div>
                                    </form>

                                    <!-- Debug form for testing -->
                                    {{-- <hr>
                                    <h6 class="text-muted">Debug Test</h6>
                                    <form action="/debug-post" method="POST">
                                        @csrf
                                        <input type="hidden" name="payment_method" value="test">
                                        <button type="submit" class="btn btn-sm btn-warning">
                                            Test Debug Form
                                        </button>
                                    </form> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection